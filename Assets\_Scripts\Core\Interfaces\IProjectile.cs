using UnityEngine;

namespace BTR
{
    /// <summary>
    /// DEPRECATED: This interface is no longer used.
    /// Use BTR.Projectiles.IProjectile instead for the new projectile system.
    /// This interface is kept temporarily to prevent compilation errors during transition.
    /// </summary>
    [System.Obsolete("Use BTR.Projectiles.IProjectile instead. This interface will be removed in a future update.")]
    public interface IProjectile_DEPRECATED
    {
        GameObject GameObject { get; }
        void Initialize(Vector3 position, Quaternion rotation);
        void Launch(Vector3 direction, float speed);
    }
}
