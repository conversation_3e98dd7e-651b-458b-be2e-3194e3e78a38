using UnityEngine;
using Chronos;
using System.Collections.Generic;
using PathologicalGames;
using System.Linq;
using BTR.EnemySystem.Behaviors;
using BTR.EnemySystem.Interfaces;
using BTR.EnemySystem;  // Add this for EnemyEvents
using BTR.Projectiles;

namespace BTR
{
    [RequireComponent(typeof(Timeline))]
    [RequireComponent(typeof(BoxCollider))]
    [RequireComponent(typeof(EnemyCleanupCoordinator))]
    public class EnemyCore : MonoBehaviour, IEnemy, IDamageable, IProjectileRegistration, ICleanupHandler
    {
        [Header("References")]
        [SerializeField] private EnemyConfiguration config;
        [SerializeField] private GameObject enemyModel;

        [Header("Base Settings")]
        [SerializeField] private float defaultHealth = 100f;
        [SerializeField] private bool isVulnerable = true;

        [Header("Status")]
        [SerializeField] private float currentHealth;
        private bool isDead = false;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool showPathfindingGizmos = true;
        [SerializeField] private bool showLineOfSightGizmos = true;
        [SerializeField] private Color pathGizmoColor = new Color(0, 1, 0, 0.5f);
        [SerializeField] private Color lineOfSightGizmoColor = new Color(1, 0, 0, 0.5f);

        [Header("Line of Sight")]
        [SerializeField] private float lineOfSightCacheTime = 0.1f;
        [SerializeField] private Transform lineOfSightOrigin;
        [SerializeField] private bool requireLineOfSight = false;
        [SerializeField] private LayerMask lineOfSightBlockingLayers = 0; // Default to nothing blocking
        private Dictionary<Vector3, (bool hasLineOfSight, float lastCheckTime)> lineOfSightCache = new();
        private const int MAX_LOS_CACHE_SIZE = 10;

        [SerializeField] private GameObject[] deathEffects;
        private MonoBehaviour[] behaviors;

        private Timeline timeline;
        private BoxCollider boxCollider;
        private bool isInitialized;
        private bool isShuttingDown;
        private Transform playerTransform;
        private Transform spawnPoint;

        private IMovementBehavior movementBehavior;
        private ICombatBehavior combatBehavior;
        private DamageVisualizationBehavior damageVisualizer;
        private List<MonoBehaviour> behaviorsList = new();
        private EnemyCleanupCoordinator cleanupCoordinator;

        private GameEvents events => GameEventsManager.Instance?.Events;

        // Properties
        public bool IsAlive => !isDead;
        public Transform Transform => transform;
        public float CurrentHealth => currentHealth;
        public float MaxHealth => config != null ? config.maxHealth : defaultHealth;
        public bool IsVulnerable => config != null ? config.isVulnerable : isVulnerable;
        public EnemyConfiguration Configuration => config;
        public EnemyConfiguration Config => config;
        public Timeline EnemyTimeline => timeline;
        public Clock EnemyClock { get; private set; }
        public Transform PlayerTransform => playerTransform;

        public event System.Action<float> OnDamageReceived;
        public event System.Action OnDeath;
        public event System.Action OnReset;
        public event System.Action OnInitialized;

        protected virtual void Awake()
        {
            timeline = GetComponent<Timeline>();
            boxCollider = GetComponent<BoxCollider>();
            cleanupCoordinator = GetComponent<EnemyCleanupCoordinator>();

            // Create spawn point for effects
            GameObject spawnPointObj = new GameObject("EffectSpawnPoint");
            spawnPoint = spawnPointObj.transform;
            spawnPoint.parent = transform;

            ValidateComponents();

            // Initialize with either config or default values
            currentHealth = MaxHealth;
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} initialized with health: {currentHealth} (From config: {config != null})");
            }

            EnemyClock = Timekeeper.instance.Clock(config != null ? config.clockKey : "Test");
        }

        private void Start()
        {
            // Initialize behaviors after all Awake() calls are complete
            InitializeBehaviors();
            InitializeDamageableParts();

            // Play spawn effects only if we have a config with effect names
            if (config != null)
            {
                SpawnEffect(config.spawnEffectName, transform.position, Quaternion.identity);
                PlayEnemySound(EnemySoundType.Spawn);
            }

            FindPlayer();

            // If we have a movement behavior, set the target
            if (movementBehavior != null && playerTransform != null)
            {
                movementBehavior.SetTarget(playerTransform);
            }

            isInitialized = true;
            OnInitialized?.Invoke();

            // Trigger spawn event
            EnemyEvents.TriggerEnemySpawned(transform);
        }

        private void FindPlayer()
        {
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
            else if (enableDebugLogs)
            {
                Debug.LogWarning($"[{GetType().Name}] Could not find player");
            }
        }

        private void ValidateComponents()
        {
            if (timeline == null)
            {
                Debug.LogError($"[{GetType().Name}] Missing Timeline component on {gameObject.name}");
            }

            if (boxCollider == null)
            {
                Debug.LogError($"[{GetType().Name}] Missing BoxCollider component on {gameObject.name}");
            }

            ValidateConfiguration();
        }

        private void ValidateConfiguration()
        {
            if (config == null)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] No configuration assigned to {gameObject.name}, using default values");
                }
                return;
            }

            List<string> validationErrors = new List<string>();

            // Validate essential configuration
            if (config.maxHealth <= 0)
                validationErrors.Add("Max health must be greater than 0");

            if (config.moveSpeed <= 0)
                validationErrors.Add("Move speed must be greater than 0");

            // Validate combat configuration
            if (config.minPlayerDistance <= 0)
                validationErrors.Add("Minimum player distance must be greater than 0");

            // Log all validation errors
            if (validationErrors.Count > 0)
            {
                Debug.LogWarning($"[{GetType().Name}] Configuration validation warnings for {gameObject.name}:\n" +
                    string.Join("\n", validationErrors.Select(e => "- " + e)));
            }
        }

        private void PlayEnemySound(EnemySoundType soundType)
        {
            try
            {
                // Use configuration-based sound events
                switch (soundType)
                {
                    case EnemySoundType.Spawn:
                        SpawnEffect(config.spawnEffectName, transform.position, Quaternion.identity);
                        break;
                    case EnemySoundType.Death:
                        SpawnEffect(config.deathEffectName, transform.position, Quaternion.identity);
                        break;
                    case EnemySoundType.Hit:
                        SpawnEffect(config.hitEffectName, transform.position, Quaternion.identity);
                        break;
                }
            }
            catch (System.Exception e)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] Failed to play {soundType} sound: {e.Message}");
                }
            }
        }

        private void Update()
        {
            if (!isInitialized || isShuttingDown) return;

            if (playerTransform == null)
            {
                FindPlayer();
                return;
            }

            float deltaTime = timeline.deltaTime;

            movementBehavior?.UpdateMovement(deltaTime);
            combatBehavior?.UpdateCombat(deltaTime);
        }

        public virtual void Initialize(EnemyConfiguration config)
        {
            if (isInitialized)
            {
                Debug.LogWarning($"[{GetType().Name}] {gameObject.name} is already initialized");
                return;
            }

            if (config == null)
            {
                Debug.LogError($"[{GetType().Name}] Cannot initialize {gameObject.name} with null configuration");
                return;
            }

            this.config = config;
            ValidateConfiguration();

            // Initialize properties
            currentHealth = config.maxHealth;
            EnemyClock = Timekeeper.instance.Clock(config.clockKey);

            if (config.enemyPrefab != null && enemyModel == null)
            {
                enemyModel = Instantiate(config.enemyPrefab, transform);
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] {gameObject.name} instantiated model from prefab");
                }
            }

            InitializeBehaviors();
            InitializeDamageableParts();

            // Play spawn effects
            SpawnEffect(config.spawnEffectName, transform.position, Quaternion.identity);
            PlayEnemySound(EnemySoundType.Spawn);

            isInitialized = true;
            OnInitialized?.Invoke();

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} initialized");
            }
        }

        private void InitializeBehaviors()
        {
            // Initialize movement behavior
            movementBehavior = GetComponent<IMovementBehavior>();
            if (movementBehavior != null)
            {
                if (movementBehavior is PathfindingMovementBehavior pathfinding)
                {
                    // Ensure EnemyCore reference is set
                    var enemyCoreSetter = pathfinding.GetType().GetField("enemyCore", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (enemyCoreSetter != null)
                    {
                        enemyCoreSetter.SetValue(pathfinding, this);
                    }
                }
                movementBehavior.Initialize(transform, config);
                behaviorsList.Add(movementBehavior as MonoBehaviour);
            }

            // Initialize combat behavior
            combatBehavior = GetComponent<ICombatBehavior>();
            if (combatBehavior != null)
            {
                combatBehavior.Initialize(transform, config);
                behaviorsList.Add(combatBehavior as MonoBehaviour);
            }

            // Initialize damage visualizer
            damageVisualizer = GetComponent<DamageVisualizationBehavior>();
            if (damageVisualizer == null)
            {
                Debug.LogWarning($"[{GetType().Name}] Missing DamageVisualizationBehavior on {gameObject.name}");
            }
            else
            {
                behaviorsList.Add(damageVisualizer);
            }
        }

        private void InitializeDamageableParts()
        {
            var parts = GetComponentsInChildren<DamageablePartBehavior>();
            foreach (var part in parts)
            {
                part.Initialize(config.hitsToKillPart);
            }
        }

        private void OnSpawned()
        {
            // Reset core state
            isDead = false;
            isShuttingDown = false;
            currentHealth = MaxHealth;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnSpawned - Setting health to {currentHealth} (MaxHealth: {MaxHealth})");
            }

            // Always reinitialize damageable parts to ensure proper health reset
            InitializeDamageableParts();

            // Re-initialize if needed
            if (!isInitialized)
            {
                InitializeBehaviors();
            }

            // Force health reset again in case something modified it during initialization
            currentHealth = MaxHealth;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnSpawned - Final health check: {currentHealth}");
            }

            // Register with managers
            if (EnemyManager.Instance != null)
            {
                EnemyManager.Instance.RegisterEnemy(this);
            }

            // Trigger spawn event
            EnemyEvents.TriggerEnemySpawned(transform);

            // Re-initialize movement behavior
            if (movementBehavior != null && movementBehavior is PathfindingMovementBehavior pathfinding)
            {
                pathfinding.Initialize(transform, config);
            }

            // Re-enable all behaviors
            if (behaviorsList != null)
            {
                foreach (var behavior in behaviorsList.Where(b => b != null))
                {
                    behavior.enabled = true;
                }
            }

            // Re-enable all colliders
            var colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = true;
            }

            // Reset all damageable parts to active state
            var parts = GetComponentsInChildren<DamageablePartBehavior>();
            foreach (var part in parts)
            {
                if (part != null)
                {
                    part.gameObject.SetActive(true);
                    part.Reset(); // This will reset the part's state including health
                }
            }

            // Play spawn effects
            if (config != null)
            {
                SpawnEffect(config.spawnEffectName, transform.position, Quaternion.identity);
                PlayEnemySound(EnemySoundType.Spawn);
            }

            // Final health verification
            if (currentHealth <= 0 || !gameObject.activeSelf)
            {
                Debug.LogError($"[{GetType().Name}] {gameObject.name} has invalid state after spawn! Health: {currentHealth}, Active: {gameObject.activeSelf}");
                // Force correct state
                currentHealth = MaxHealth;
                gameObject.SetActive(true);
            }

            // Trigger reset event
            OnReset?.Invoke();
        }

        private void OnDespawned()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnDespawned - Health before reset: {currentHealth}");
            }

            // Reset core state first
            isDead = false;
            isShuttingDown = false;
            currentHealth = MaxHealth;

            // Reset all damageable parts
            var parts = GetComponentsInChildren<DamageablePartBehavior>();
            foreach (var part in parts)
            {
                if (part != null)
                {
                    part.Reset();
                }
            }

            // Unregister from managers
            if (EnemyManager.Instance != null)
            {
                EnemyManager.Instance.UnregisterEnemy(this);
            }

            if (EnemyPathManager.Instance != null)
            {
                EnemyPathManager.Instance.UnregisterEnemy(this);
            }

            // Disable all behaviors
            if (behaviorsList != null)
            {
                foreach (var behavior in behaviorsList.Where(b => b != null))
                {
                    behavior.enabled = false;
                }
            }

            // Disable all colliders
            var colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            // Reset damage visualization
            if (damageVisualizer != null)
            {
                // Reset all renderers to their default state
                var renderers = GetComponentsInChildren<Renderer>();
                foreach (var renderer in renderers)
                {
                    damageVisualizer.OnDamaged(0); // This will trigger a reset of the visualization
                }
                damageVisualizer.enabled = false;
            }

            // Clear line of sight cache
            lineOfSightCache.Clear();

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnDespawned - Health after reset: {currentHealth}");
            }
        }

        public virtual void Cleanup()
        {
            if (isShuttingDown) return;
            isShuttingDown = true;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} initiating cleanup");
            }

            cleanupCoordinator.InitiateCleanup();
        }

        private void OnEnable()
        {
            // Force health reset on enable as a safety measure
            currentHealth = MaxHealth;
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnEnable - Setting health to {currentHealth}");
            }
        }

        private void OnDisable()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnDisable - Current health: {currentHealth}");
            }
        }

        public void OnFinalCleanup()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnFinalCleanup - Health before reset: {currentHealth}");
            }

            // Clean up spawn point
            if (spawnPoint != null)
            {
                Destroy(spawnPoint.gameObject);
            }

            // Reset state before returning to pool
            isDead = false;
            isShuttingDown = false;
            currentHealth = MaxHealth;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnFinalCleanup - Health after reset: {currentHealth}");
            }

            // Deactivate the GameObject
            if (gameObject != null)
            {
                gameObject.SetActive(false);
            }
        }

        // ICleanupHandler Implementation
        public void OnImmediateCleanup()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} OnImmediateCleanup - Current health: {currentHealth}");
            }

            // Stop all active behaviors
            if (behaviorsList != null)
            {
                foreach (var behavior in behaviorsList)
                {
                    if (behavior != null && behavior is IDeathHandler deathHandler)
                    {
                        deathHandler.OnDeath();
                    }
                }
            }

            // Disable parts without damaging them
            var parts = GetComponentsInChildren<DamageablePartBehavior>();
            if (parts != null)
            {
                foreach (var part in parts)
                {
                    if (part != null)
                    {
                        part.gameObject.SetActive(false);
                    }
                }
            }

            // Unregister from managers
            if (EnemyManager.Instance != null)
            {
                EnemyManager.Instance.UnregisterEnemy(this);
            }

            if (EnemyPathManager.Instance != null)
            {
                EnemyPathManager.Instance.UnregisterEnemy(this);
            }
        }

        public bool OnDeferredCleanup()
        {
            // Play death effects only if config exists
            if (config != null && !string.IsNullOrEmpty(config.deathEffectName))
            {
                SpawnEffect(config.deathEffectName, transform.position, Quaternion.identity);
            }

            PlayEnemySound(EnemySoundType.Death);
            OnDeath?.Invoke();

            return true; // Death effects are fire-and-forget
        }

        public int CleanupPriority => 0; // EnemyCore should be cleaned up first

        public void RegisterProjectile(IProjectile projectile)
        {
            if (projectile == null)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Attempted to register null projectile");
                }
                return;
            }
            // Implementation for registering projectiles
            RegisterProjectiles();
        }

        public void RegisterProjectiles()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] {gameObject.name} registering projectiles");
            }

            // Note: Projectile registration should be handled by the spawning system
            // This method is kept for interface compatibility
        }

        public bool CheckLineOfSight(Vector3 targetPosition)
        {
            // If line of sight is not required, always return true
            if (!requireLineOfSight)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Line of sight check skipped - Not required for {gameObject.name}");
                }
                return true;
            }

            // If we don't have a line of sight origin, use transform
            if (lineOfSightOrigin == null)
            {
                lineOfSightOrigin = transform;
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] Line of sight origin is null, using transform instead for {gameObject.name}");
                }
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Checking line of sight from {lineOfSightOrigin.position} to {targetPosition} for {gameObject.name}");
            }

            // Check cache first
            if (lineOfSightCache.TryGetValue(targetPosition, out var cacheEntry))
            {
                if (Time.time - cacheEntry.lastCheckTime < lineOfSightCacheTime)
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[{GetType().Name}] Using cached line of sight result: {cacheEntry.hasLineOfSight} (age: {Time.time - cacheEntry.lastCheckTime:F3}s) for {gameObject.name}");
                    }
                    return cacheEntry.hasLineOfSight;
                }
                else
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[{GetType().Name}] Cached line of sight result expired (age: {Time.time - cacheEntry.lastCheckTime:F3}s) for {gameObject.name}");
                    }
                }
            }

            // Perform actual line of sight check
            Vector3 direction = targetPosition - lineOfSightOrigin.position;
            float distance = direction.magnitude;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Line of sight check - Direction: {direction.normalized}, Distance: {distance:F2} for {gameObject.name}");
            }

            // Determine which layer mask to use
            LayerMask blockingLayers = config != null && config.obstacleLayerMask != 0
                ? config.obstacleLayerMask
                : lineOfSightBlockingLayers;

            // Always exclude enemy and projectile layers from blocking
            int excludeLayers = LayerMask.GetMask("Enemy", "EnemyProjectile");
            int finalMask = blockingLayers & ~excludeLayers;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Line of sight using layer mask: {finalMask} (Config mask: {(config != null ? config.obstacleLayerMask : 0)}, Inspector mask: {lineOfSightBlockingLayers}) for {gameObject.name}");
            }

            bool hasLineOfSight = false;

            // If no blocking layers are defined, we have line of sight
            if (finalMask == 0)
            {
                hasLineOfSight = true;
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Line of sight check - No blocking layers defined, automatically passing for {gameObject.name}");
                }
            }
            else if (Physics.Raycast(lineOfSightOrigin.position, direction.normalized, out RaycastHit hit, distance, finalMask))
            {
                // If we hit the player or something on the player layer, we have line of sight
                bool isPlayerTag = hit.transform.CompareTag("Player");
                bool isPlayerLayer = hit.transform.gameObject.layer == LayerMask.NameToLayer("Player");
                hasLineOfSight = isPlayerTag || isPlayerLayer;

                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemyCore] Line of sight raycast hit: {hit.transform.name}, Tag: {hit.transform.tag}, Layer: {LayerMask.LayerToName(hit.transform.gameObject.layer)}, IsPlayerTag: {isPlayerTag}, IsPlayerLayer: {isPlayerLayer}, HasLineOfSight: {hasLineOfSight} for {gameObject.name}");
                }
            }
            else
            {
                // If we didn't hit anything, we have line of sight
                hasLineOfSight = true;
                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemyCore] Line of sight raycast hit nothing, clear line of sight for {gameObject.name}");
                }
            }

            // Update cache
            if (lineOfSightCache.Count >= MAX_LOS_CACHE_SIZE)
            {
                // Remove oldest entry if cache is full
                var oldestEntry = lineOfSightCache.OrderBy(kvp => kvp.Value.lastCheckTime).First();
                lineOfSightCache.Remove(oldestEntry.Key);
                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemyCore] Line of sight cache full, removed oldest entry for {gameObject.name}");
                }
            }

            lineOfSightCache[targetPosition] = (hasLineOfSight, Time.time);
            if (enableDebugLogs)
            {
                Debug.Log($"[EnemyCore] Line of sight check result: {hasLineOfSight}, cached at time {Time.time:F2} for {gameObject.name}");

                // Log additional info about the enemy state
                Debug.Log($"[EnemyCore] Enemy state - IsAlive: {IsAlive}, IsVulnerable: {IsVulnerable}, HasTarget: {playerTransform != null}, Distance to player: {(playerTransform != null ? Vector3.Distance(transform.position, playerTransform.position) : -1):F2}");
            }

            return hasLineOfSight;
        }

        protected virtual void SpawnEffect(string effectName, Vector3 position, Quaternion rotation)
        {
            if (!string.IsNullOrEmpty(effectName) && PoolManager.Pools.ContainsKey(effectName))
            {
                var pool = PoolManager.Pools[effectName];
                if (pool != null)
                {
                    spawnPoint.position = position;
                    spawnPoint.rotation = rotation;

                    var spawnedTransform = pool.Spawn(spawnPoint, spawnPoint);
                    if (spawnedTransform == null)
                    {
                        Debug.LogWarning($"[{GetType().Name}] Failed to spawn effect: {effectName}");
                    }
                }
            }
        }

        // IDamageable implementation
        public void Damage(float amount)
        {
            TakeDamage(amount);
        }

        // Internal damage handling implementation
        public void TakeDamage(float amount)
        {
            if (!IsAlive || !IsVulnerable || amount <= 0)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemyCore] {gameObject.name} - Damage rejected: Alive={IsAlive}, Vulnerable={IsVulnerable}, Amount={amount}");
                }
                return;
            }

            float oldHealth = currentHealth;
            currentHealth = Mathf.Max(0f, currentHealth - amount);
            if (enableDebugLogs)
            {
                Debug.Log($"[EnemyCore] {gameObject.name} took {amount} damage. Health: {oldHealth} -> {currentHealth}");
            }

            damageVisualizer?.OnDamaged(amount);
            events?.TriggerEnemyDamaged(transform, amount);
            OnDamageReceived?.Invoke(amount);

            if (currentHealth <= 0)
            {
                Die();
            }
            else
            {
                PlayEnemySound(EnemySoundType.Hit);
            }
        }

        public void Die()
        {
            if (isDead) return;
            isDead = true;

            try
            {
                // Register for cleanup tracking
                if (EnemyManager.Instance != null)
                {
                    EnemyManager.Instance.RegisterForCleanup(this);
                }

                // Disable behaviors
                if (behaviors == null)
                {
                    behaviors = GetComponents<MonoBehaviour>();
                }

                foreach (var behavior in behaviors)
                {
                    if (behavior != null && behavior != this)
                    {
                        behavior.enabled = false;
                    }
                }

                // Play death effects
                if (deathEffects != null)
                {
                    foreach (var effect in deathEffects)
                    {
                        if (effect != null)
                        {
                            Instantiate(effect, transform.position, Quaternion.identity);
                        }
                    }
                }

                // Notify cleanup coordinator
                if (cleanupCoordinator != null)
                {
                    cleanupCoordinator.InitiateCleanup();
                }
                else
                {
                    Debug.LogWarning($"[ENEMY] Enemy {gameObject.name} is missing CleanupCoordinator! Cannot pool/cleanup properly.");
                    Destroy(gameObject);
                }

                // Unregister from cleanup tracking
                if (EnemyManager.Instance != null)
                {
                    EnemyManager.Instance.UnregisterFromCleanup(this);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ENEMY] Error during death: {e.Message}\n{e.StackTrace}");

                // Ensure we unregister even if there's an error
                if (EnemyManager.Instance != null)
                {
                    EnemyManager.Instance.UnregisterFromCleanup(this);
                }
            }
        }

        // IEnemy interface implementation
        public void HandleDeath()
        {
            Die();
        }

        private void OnDrawGizmos()
        {
            if (!Application.isPlaying || !showPathfindingGizmos) return;

            // Draw path if available
            if (movementBehavior is PathfindingMovementBehavior pathfinding)
            {
                pathfinding.DrawPathGizmos(pathGizmoColor);
            }

            // Draw line of sight if enabled
            if (showLineOfSightGizmos && playerTransform != null)
            {
                bool hasLOS = Application.isPlaying ? CheckLineOfSight(playerTransform.position) : false;
                Gizmos.color = hasLOS ? Color.green : lineOfSightGizmoColor;
                Gizmos.DrawLine(transform.position, playerTransform.position);
            }

            // Draw cached LOS checks
            if (showLineOfSightGizmos && Application.isPlaying)
            {
                foreach (var cache in lineOfSightCache)
                {
                    Gizmos.color = cache.Value.hasLineOfSight ?
                        new Color(0, 1, 0, 0.2f) :
                        new Color(1, 0, 0, 0.2f);
                    Gizmos.DrawLine(transform.position, cache.Key);
                }
            }
        }

        public void HandleLineOfSightResult(bool hasLineOfSight)
        {
            bool previousLineOfSight = CheckLineOfSight(playerTransform.position);
            if (previousLineOfSight != hasLineOfSight && enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Line of sight changed to {hasLineOfSight} for {gameObject.name}");
            }
        }

        public void SetLineOfSightOrigin(Transform origin)
        {
            lineOfSightOrigin = origin;
            if (enableDebugLogs)
            {
                Debug.Log($"[EnemyCore] Line of sight origin set to: {(origin != null ? origin.name : "null")}");
            }
        }
    }
}