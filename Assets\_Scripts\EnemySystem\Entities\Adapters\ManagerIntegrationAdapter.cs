using UnityEngine;
using BTR.EnemySystem.Entities;
using UltimateSpawner;

namespace BTR.EnemySystem.Adapters
{
    /// <summary>
    /// Adapter that ensures new entity system integrates properly with existing managers.
    /// Handles registration with EnemyManager, wave spawning, and event systems.
    /// </summary>
    [TemporaryAdapter("EnemySystem/Managers/DirectIntegration")]
    public class ManagerIntegrationAdapter : MonoBehaviour
    {
        [Header("Integration Settings")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool autoRegisterWithManagers = true;
        [SerializeField] private bool forwardEventsToLegacySystem = true;

        // Component references
        private IEntity entity;
        private ICombatEntity combatEntity;
        private EnemyCore legacyCore;

        // Manager references
        private EnemyManager enemyManager;
        private bool isRegisteredWithManager = false;

        private void Awake()
        {
            // Get component references
            entity = GetComponent<IEntity>();
            combatEntity = GetComponent<ICombatEntity>();
            legacyCore = GetComponent<EnemyCore>();

            if (entity == null)
            {
                Debug.LogError($"[ManagerIntegrationAdapter] No IEntity found on {gameObject.name}");
                enabled = false;
                return;
            }

            // Get manager references
            enemyManager = EnemyManager.Instance;

            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Adapter initialized for {gameObject.name}");
            }
        }

        private void Start()
        {
            if (autoRegisterWithManagers)
            {
                RegisterWithManagers();
            }

            SetupEventForwarding();
        }

        private void RegisterWithManagers()
        {
            try
            {
                // Register with EnemyManager
                RegisterWithEnemyManager();

                // Register with other managers as needed
                RegisterWithOtherManagers();

                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Successfully registered {gameObject.name} with managers");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ManagerIntegrationAdapter] Failed to register {gameObject.name} with managers: {e.Message}");
            }
        }

        private void RegisterWithEnemyManager()
        {
            if (enemyManager == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[ManagerIntegrationAdapter] EnemyManager not found for {gameObject.name}");
                }
                return;
            }

            // If we have a legacy core, let it handle registration
            if (legacyCore != null)
            {
                // Legacy core will handle its own registration
                isRegisteredWithManager = true;
                return;
            }

            // For pure new entities, register directly with the new entity system
            if (combatEntity != null)
            {
                enemyManager.RegisterCombatEntity(combatEntity);
                isRegisteredWithManager = true;

                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Successfully registered new combat entity {gameObject.name} with EnemyManager");
                }
            }
            else if (entity != null)
            {
                // For non-combat entities, we could add a separate registration method if needed
                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} is not a combat entity - no EnemyManager registration needed");
                }
                isRegisteredWithManager = true;
            }
        }

        private void RegisterWithOtherManagers()
        {
            // Register with wave spawning system if applicable
            RegisterWithWaveSystem();

            // Register with cleanup system
            RegisterWithCleanupSystem();

            // Register with event system
            RegisterWithEventSystem();
        }

        private void RegisterWithWaveSystem()
        {
            // Check if this entity was spawned by a wave spawner
            var waveSpawner = GetComponentInParent<WaveSpawnController>();
            if (waveSpawner != null)
            {
                // Notify wave spawner of new entity
                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Registering {gameObject.name} with wave spawner");
                }

                // This would integrate with your wave spawning system
                // For now, we'll trigger the appropriate events
                TriggerSpawnEvents();
            }
        }

        private void RegisterWithCleanupSystem()
        {
            if (combatEntity != null)
            {
                // Register for cleanup when entity dies
                combatEntity.OnDeath += HandleEntityDeath;

                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Registered {gameObject.name} for cleanup handling");
                }
            }
        }

        private void RegisterWithEventSystem()
        {
            if (entity != null)
            {
                // Register for entity lifecycle events
                entity.OnInitialized += HandleEntityInitialized;
                entity.OnActivated += HandleEntityActivated;
                entity.OnDeactivated += HandleEntityDeactivated;
            }

            if (combatEntity != null)
            {
                // Register for combat events
                combatEntity.OnDamageReceived += HandleEntityDamaged;
                combatEntity.OnDeath += HandleEntityDeath;
            }
        }

        private void SetupEventForwarding()
        {
            if (!forwardEventsToLegacySystem)
                return;

            // Forward new entity events to legacy event system
            if (combatEntity != null)
            {
                combatEntity.OnDamageReceived += (damage) =>
                {
                    // Forward to EnemyEvents system
                    EnemyEvents.TriggerEnemyDamaged(transform, damage);
                };

                combatEntity.OnDeath += () =>
                {
                    // Forward to EnemyEvents system
                    EnemyEvents.TriggerEnemyDied(transform);
                };
            }
        }

        private void TriggerSpawnEvents()
        {
            // Trigger enemy spawned event for legacy systems
            EnemyEvents.TriggerEnemySpawned(transform);

            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Triggered spawn events for {gameObject.name}");
            }
        }

        private void HandleEntityInitialized()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} initialized");
            }

            // Trigger any initialization events needed by legacy systems
            TriggerSpawnEvents();
        }

        private void HandleEntityActivated()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} activated");
            }
        }

        private void HandleEntityDeactivated()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} deactivated");
            }
        }

        private void HandleEntityDamaged(float damage)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} took {damage} damage");
            }
        }

        private void HandleEntityDeath()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Entity {gameObject.name} died");
            }

            // Unregister from managers
            UnregisterFromManagers();
        }

        private void UnregisterFromManagers()
        {
            try
            {
                // Unregister from EnemyManager
                if (isRegisteredWithManager && enemyManager != null)
                {
                    // If we have legacy core, let it handle unregistration
                    if (legacyCore != null)
                    {
                        // Legacy core will handle its own unregistration
                    }
                    else if (combatEntity != null)
                    {
                        // For pure new entities, unregister directly
                        enemyManager.UnregisterCombatEntity(combatEntity);

                        if (enableDebugLogs)
                        {
                            Debug.Log($"[ManagerIntegrationAdapter] Successfully unregistered new combat entity {gameObject.name} from EnemyManager");
                        }
                    }

                    isRegisteredWithManager = false;
                }

                if (enableDebugLogs)
                {
                    Debug.Log($"[ManagerIntegrationAdapter] Successfully unregistered {gameObject.name} from managers");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ManagerIntegrationAdapter] Failed to unregister {gameObject.name} from managers: {e.Message}");
            }
        }

        private void OnDestroy()
        {
            // Clean up event subscriptions
            if (entity != null)
            {
                entity.OnInitialized -= HandleEntityInitialized;
                entity.OnActivated -= HandleEntityActivated;
                entity.OnDeactivated -= HandleEntityDeactivated;
            }

            if (combatEntity != null)
            {
                combatEntity.OnDamageReceived -= HandleEntityDamaged;
                combatEntity.OnDeath -= HandleEntityDeath;
            }

            // Ensure unregistration
            if (isRegisteredWithManager)
            {
                UnregisterFromManagers();
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[ManagerIntegrationAdapter] Adapter destroyed for {gameObject.name}");
            }
        }

        // Public API for manual control
        public void ForceRegisterWithManagers()
        {
            RegisterWithManagers();
        }

        public void ForceUnregisterFromManagers()
        {
            UnregisterFromManagers();
        }

        public bool IsRegisteredWithManager => isRegisteredWithManager;

        // Register adapter usage with migration tracker
        private void OnEnable()
        {

        }

        private void OnDisable()
        {

        }
    }
}
