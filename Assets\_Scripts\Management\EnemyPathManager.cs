using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using BTR;
using BTR.EnemySystem.Entities;

namespace BTR
{
    public class EnemyPathManager : MonoBehaviour
    {
        public static EnemyPathManager Instance { get; private set; }

        // Legacy enemy tracking
        private List<EnemyCore> trackedEnemies = new List<EnemyCore>();

        // New entity tracking
        private List<IEntity> trackedEntities = new List<IEntity>();

        [Header("Pathfinding Optimization")]
        [SerializeField] private float pathCacheTime = 0.3f;
        [SerializeField] private float pathCacheDistance = 2f;
        [SerializeField] private int maxPathRequestsPerFrame = 5;
        [SerializeField] private float gridCellSize = 5f;
        [SerializeField] private float updateInterval = 0.05f;
        [SerializeField] private float significantMovementThreshold = 0.3f;

        // Spatial grid for quick neighbor lookups
        private Dictionary<Vector2Int, List<EnemyCore>> spatialGrid = new Dictionary<Vector2Int, List<EnemyCore>>();
        private Dictionary<EnemyCore, Vector3> lastKnownPositions = new Dictionary<EnemyCore, Vector3>();
        private float nextUpdateTime;
        private int spatialGridUpdateIndex = 0;

        // Path caching system
        private class CachedPath
        {
            public Vector3 startPos;
            public Vector3 endPos;
            public List<Vector3> path;
            public float timestamp;
        }
        private Dictionary<EnemyCore, CachedPath> pathCache = new Dictionary<EnemyCore, CachedPath>();
        private Queue<(float priority, EnemyCore enemy, IPathHandler handler)> priorityPathQueue =
            new Queue<(float priority, EnemyCore enemy, IPathHandler handler)>();

        private const int ENEMIES_PER_SPATIAL_UPDATE = 20;
        private const int MAX_CACHED_PATHS = 100;
        private const float PATH_PRIORITY_DISTANCE_WEIGHT = 1.5f;

        [Header("Debug Visualization")]
        [SerializeField] private bool showDebugVisualization = true;
        [SerializeField] private Color gridColor = new Color(0.5f, 0.5f, 0.5f, 0.3f);
        [SerializeField] private Color cachedPathColor = Color.green;
        private Color requestQueueColor = Color.red;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            trackedEnemies = new List<EnemyCore>();
            spatialGrid = new Dictionary<Vector2Int, List<EnemyCore>>();
            pathCache = new Dictionary<EnemyCore, CachedPath>();
            lastKnownPositions = new Dictionary<EnemyCore, Vector3>();
        }

        private void Start()
        {
            // Sync with EnemyManager if it exists
            if (EnemyManager.Instance != null)
            {
                SyncWithEnemyManager();
            }

            // Start path update coroutine
            StartCoroutine(UpdatePaths());
        }

        private System.Collections.IEnumerator UpdatePaths()
        {
            while (true)
            {
                foreach (var enemy in trackedEnemies.ToList())
                {
                    if (enemy != null)
                    {
                        var movementBehavior = enemy.GetComponent<PathfindingMovementBehavior>();
                        if (movementBehavior != null)
                        {
                            UpdateEnemyPath(enemy, movementBehavior);
                        }
                    }
                }
                yield return new WaitForSeconds(updateInterval);
            }
        }

        private void UpdateEnemyPath(EnemyCore enemy, PathfindingMovementBehavior movementBehavior)
        {
            if (enemy == null || enemy.PlayerTransform == null) return;

            Vector3 currentPos = enemy.transform.position;
            Vector3 targetPos = enemy.PlayerTransform.position;

            // Check if we need to update the path based on cache settings
            if (ShouldUpdatePath(enemy, currentPos, targetPos))
            {
                // Queue path request with priority based on distance
                float priority = Vector3.Distance(currentPos, targetPos) * PATH_PRIORITY_DISTANCE_WEIGHT;
                priorityPathQueue.Enqueue((priority, enemy, movementBehavior));
            }
        }

        private bool ShouldUpdatePath(EnemyCore enemy, Vector3 currentPos, Vector3 targetPos)
        {
            // TO DO: implement logic to determine if path update is needed
            return true;
        }

        // Legacy enemy registration
        public void RegisterEnemy(EnemyCore enemy)
        {
            if (!trackedEnemies.Contains(enemy))
            {
                trackedEnemies.Add(enemy);
            }
        }

        public void UnregisterEnemy(EnemyCore enemy)
        {
            trackedEnemies.Remove(enemy);
            lastKnownPositions.Remove(enemy);
            pathCache.Remove(enemy);

            // Clean up spatial grid
            foreach (var cell in spatialGrid.Values)
            {
                cell.Remove(enemy);
            }
        }

        // New entity registration
        public void RegisterEntity(IEntity entity)
        {
            if (!trackedEntities.Contains(entity))
            {
                trackedEntities.Add(entity);
            }
        }

        public void UnregisterEntity(IEntity entity)
        {
            trackedEntities.Remove(entity);
            // Note: New entities don't use the legacy pathfinding cache system
            // They use the centralized pathfinding service in EnemyManager
        }

        private void SyncWithEnemyManager()
        {
            if (EnemyManager.Instance != null)
            {
                // Sync legacy enemies
                foreach (var enemy in EnemyManager.Instance.Enemies)
                {
                    RegisterEnemy(enemy);
                }

                // Sync new entities
                foreach (var entity in EnemyManager.Instance.CombatEntities)
                {
                    RegisterEntity(entity);
                }
            }
        }

        private void Update()
        {
            if (Time.time < nextUpdateTime) return;
            nextUpdateTime = Time.time + updateInterval;

            UpdateSpatialGrid();
            ProcessPathRequests();
        }

        private Vector2Int GetGridCell(Vector3 position)
        {
            return new Vector2Int(
                Mathf.FloorToInt(position.x / gridCellSize),
                Mathf.FloorToInt(position.z / gridCellSize)
            );
        }

        private void UpdateSpatialGrid()
        {
            int enemiesPerFrame = Mathf.Min(ENEMIES_PER_SPATIAL_UPDATE, trackedEnemies.Count);
            int processedCount = 0;

            while (processedCount < enemiesPerFrame && spatialGridUpdateIndex < trackedEnemies.Count)
            {
                var enemy = trackedEnemies[spatialGridUpdateIndex];
                if (enemy == null || !enemy.gameObject.activeInHierarchy)
                {
                    trackedEnemies.RemoveAt(spatialGridUpdateIndex);
                    lastKnownPositions.Remove(enemy);
                    continue;
                }

                Vector3 currentPos = enemy.transform.position;
                if (!lastKnownPositions.TryGetValue(enemy, out Vector3 lastPos) ||
                    Vector3.SqrMagnitude(currentPos - lastPos) > significantMovementThreshold * significantMovementThreshold)
                {
                    if (lastKnownPositions.ContainsKey(enemy))
                    {
                        Vector2Int oldCell = GetGridCell(lastPos);
                        if (spatialGrid.TryGetValue(oldCell, out var oldCellEnemies))
                        {
                            oldCellEnemies.Remove(enemy);
                            if (oldCellEnemies.Count == 0)
                            {
                                spatialGrid.Remove(oldCell);
                            }
                        }
                    }

                    Vector2Int newCell = GetGridCell(currentPos);
                    if (!spatialGrid.TryGetValue(newCell, out var cellEnemies))
                    {
                        cellEnemies = new List<EnemyCore>();
                        spatialGrid[newCell] = cellEnemies;
                    }
                    if (!cellEnemies.Contains(enemy))
                    {
                        cellEnemies.Add(enemy);
                    }

                    lastKnownPositions[enemy] = currentPos;
                }

                spatialGridUpdateIndex++;
                processedCount++;
            }

            if (spatialGridUpdateIndex >= trackedEnemies.Count)
            {
                spatialGridUpdateIndex = 0;
            }
        }

        public void RequestPath(EnemyCore enemy, IPathHandler handler)
        {
            if (enemy == null || handler == null) return;

            float priority = CalculatePathPriority(enemy, handler);
            var request = (priority, enemy, handler);

            if (!priorityPathQueue.Any(x => x.enemy == enemy))
            {
                priorityPathQueue.Enqueue(request);
            }
        }

        private float CalculatePathPriority(EnemyCore enemy, IPathHandler handler)
        {
            if (enemy == null || handler == null) return 0f;

            Vector3 targetPos = handler.GetTargetPosition();
            float distance = Vector3.Distance(enemy.transform.position, targetPos);
            return 1f / (1f + distance * PATH_PRIORITY_DISTANCE_WEIGHT);
        }

        private void ProcessPathRequests()
        {
            if (priorityPathQueue.Count == 0) return;

            var sortedRequests = priorityPathQueue
                .OrderByDescending(x => x.priority)
                .Take(maxPathRequestsPerFrame)
                .ToList();

            priorityPathQueue.Clear();
            foreach (var request in sortedRequests.Skip(maxPathRequestsPerFrame))
            {
                priorityPathQueue.Enqueue(request);
            }

            foreach (var (priority, enemy, handler) in sortedRequests.Take(maxPathRequestsPerFrame))
            {
                if (enemy == null || !enemy.gameObject.activeInHierarchy || handler == null) continue;

                Vector2Int cell = GetGridCell(enemy.transform.position);
                bool foundCachedPath = false;

                if (pathCache.Count > MAX_CACHED_PATHS)
                {
                    var oldestPaths = pathCache
                        .OrderBy(x => x.Value.timestamp)
                        .Take(pathCache.Count - MAX_CACHED_PATHS + 1);

                    foreach (var path in oldestPaths)
                    {
                        pathCache.Remove(path.Key);
                    }
                }

                for (int dx = -1; dx <= 1; dx++)
                {
                    for (int dz = -1; dz <= 1; dz++)
                    {
                        if (dx == 0 && dz == 0) continue;

                        Vector2Int neighborCell = cell + new Vector2Int(dx, dz);
                        if (TryGetCachedPath(enemy, neighborCell, Time.time, out var cachedPath))
                        {
                            handler.OnPathComplete(cachedPath.path);
                            foundCachedPath = true;
                            break;
                        }
                    }
                    if (foundCachedPath) break;
                }

                if (!foundCachedPath)
                {
                    Vector3 startPos = enemy.transform.position;
                    Vector3 targetPos = handler.GetTargetPosition();
                    var path = new List<Vector3> { startPos, targetPos };

                    var newCache = new CachedPath
                    {
                        startPos = startPos,
                        endPos = targetPos,
                        path = path,
                        timestamp = Time.time
                    };
                    pathCache[enemy] = newCache;
                    handler.OnPathComplete(path);
                }
            }
        }

        private bool TryGetCachedPath(EnemyCore enemy, Vector2Int cell, float currentTime, out CachedPath cachedPath)
        {
            cachedPath = null;
            if (!spatialGrid.TryGetValue(cell, out var cellEnemies)) return false;

            foreach (var otherEnemy in cellEnemies)
            {
                if (otherEnemy == enemy) continue;

                if (pathCache.TryGetValue(otherEnemy, out var existingPath))
                {
                    if (currentTime - existingPath.timestamp <= pathCacheTime &&
                        Vector3.Distance(enemy.transform.position, existingPath.startPos) <= pathCacheDistance)
                    {
                        cachedPath = new CachedPath
                        {
                            startPos = enemy.transform.position,
                            endPos = existingPath.endPos,
                            path = new List<Vector3>(existingPath.path),
                            timestamp = currentTime
                        };
                        pathCache[enemy] = cachedPath;
                        return true;
                    }
                }
            }
            return false;
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying) return;

            DrawSpatialGrid();
            DrawCachedPaths();
            DrawPathRequests();
        }

        private void DrawSpatialGrid()
        {
            Gizmos.color = gridColor;
            foreach (var cell in spatialGrid.Keys)
            {
                Vector3 cellCenter = new Vector3(
                    (cell.x + 0.5f) * gridCellSize,
                    0,
                    (cell.y + 0.5f) * gridCellSize
                );
                Vector3 cellSize = new Vector3(gridCellSize, 0.1f, gridCellSize);
                Gizmos.DrawWireCube(cellCenter, cellSize);

                if (spatialGrid[cell].Count > 1)
                {
                    Gizmos.color = Color.cyan;
                    var enemies = spatialGrid[cell].Where(e => e != null).ToList();
                    for (int i = 0; i < enemies.Count; i++)
                    {
                        for (int j = i + 1; j < enemies.Count; j++)
                        {
                            Gizmos.DrawLine(
                                enemies[i].transform.position + Vector3.up,
                                enemies[j].transform.position + Vector3.up
                            );
                        }
                    }
                }
            }
        }

        private void DrawCachedPaths()
        {
            float currentTime = Time.time;
            foreach (var kvp in pathCache)
            {
                if (kvp.Key == null) continue;

                var cachedPath = kvp.Value;
                float pathAge = currentTime - cachedPath.timestamp;
                float ageRatio = pathAge / pathCacheTime;

                Color pathColor = Color.Lerp(cachedPathColor, Color.clear, ageRatio);
                Gizmos.color = pathColor;

                if (cachedPath.path != null && cachedPath.path.Count > 0)
                {
                    for (int i = 0; i < cachedPath.path.Count - 1; i++)
                    {
                        Gizmos.DrawLine(
                            cachedPath.path[i] + Vector3.up,
                            cachedPath.path[i + 1] + Vector3.up
                        );
                    }

                    foreach (var point in cachedPath.path)
                    {
                        Gizmos.DrawWireSphere(point + Vector3.up, 0.2f);
                    }
                }

                Gizmos.DrawLine(
                    kvp.Key.transform.position + Vector3.up,
                    cachedPath.startPos + Vector3.up
                );
            }
        }

        private void DrawPathRequests()
        {
            Gizmos.color = requestQueueColor;
            var queueSnapshot = priorityPathQueue.ToArray();

            foreach (var (priority, enemy, handler) in queueSnapshot)
            {
                if (enemy != null && handler != null)
                {
                    Vector3 start = enemy.transform.position + Vector3.up;
                    Vector3 end = handler.GetTargetPosition() + Vector3.up;
                    Gizmos.DrawLine(start, end);
                    Gizmos.DrawWireSphere(end, 0.3f);
                }
            }

            if (queueSnapshot.Length > 0)
            {
#if UNITY_EDITOR
                UnityEditor.Handles.BeginGUI();
                var view = UnityEditor.SceneView.currentDrawingSceneView;
                if (view != null)
                {
                    Vector3 screenPos = view.camera.WorldToScreenPoint(Vector3.zero + Vector3.up * 5);
                    if (screenPos.z > 0)
                    {
                        var style = new GUIStyle();
                        style.normal.textColor = requestQueueColor;
                        style.fontSize = 12;
                        style.fontStyle = FontStyle.Bold;
                        UnityEditor.Handles.Label(
                            Vector3.up * 5,
                            $"Path Requests: {queueSnapshot.Length}",
                            style
                        );
                    }
                }
                UnityEditor.Handles.EndGUI();
#endif
            }
        }

        private void OnGUI()
        {
            if (!showDebugVisualization) return;

            GUILayout.BeginArea(new Rect(10, 10, 250, 150));
            GUILayout.Label("=== Legacy Pathfinding ===");
            GUILayout.Label($"Active Paths: {pathCache.Count}");
            GUILayout.Label($"Queue Size: {priorityPathQueue.Count}");
            GUILayout.Label($"Grid Cells: {spatialGrid.Count}");

            // Display centralized pathfinding stats if available
            if (EnemyManager.Instance != null)
            {
                GUILayout.Space(10);
                EnemyManager.Instance.DisplayPathfindingStatsGUI();
            }

            GUILayout.EndArea();
        }

        private void OnEnable()
        {
            if (EnemyManager.Instance != null)
            {
                SyncWithEnemyManager();
            }
        }

        private void OnDisable()
        {
            trackedEnemies.Clear();
            lastKnownPositions.Clear();
            pathCache.Clear();
            spatialGrid.Clear();
        }
    }
}