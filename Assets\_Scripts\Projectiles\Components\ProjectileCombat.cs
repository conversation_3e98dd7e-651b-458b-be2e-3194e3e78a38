using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile combat logic.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileCombat : MonoBehaviour, IProjectileCombat
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;
        
        // Placeholder fields - will be populated in Phase 2
        [SerializeField] private float damageAmount = 10f;
        [SerializeField] private float damageMultiplier = 1f;
        [SerializeField] private LayerMask collisionLayers = -1;
        #endregion

        #region IProjectileCombat Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;
            isInitialized = true;
            
            Debug.Log($"[ProjectileCombat] Initialized for projectile {projectile.EntityID}");
        }

        public void ApplyDamage(IDamageable target, Vector3 hitPoint)
        {
            if (!isInitialized || target == null) return;
            
            // TODO: Migrate damage application logic from ProjectileStateBased in Phase 2
            float finalDamage = CalculateDamage(target);
            target.TakeDamage(finalDamage);
            
            Debug.Log($"[ProjectileCombat] Applied {finalDamage} damage to {target}");
        }

        public float CalculateDamage(IDamageable target)
        {
            // TODO: Implement damage calculation logic in Phase 2
            return damageAmount * damageMultiplier;
        }

        public void HandleCollision(Collision collision)
        {
            if (!isInitialized) return;
            
            // TODO: Migrate OnCollisionEnter logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileCombat] Collision with {collision.gameObject.name}");
        }

        public void HandleTrigger(Collider other)
        {
            if (!isInitialized) return;
            
            // TODO: Migrate OnTriggerEnter logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileCombat] Trigger with {other.gameObject.name}");
        }

        public void SetDamage(float damage)
        {
            damageAmount = damage;
            Debug.Log($"[ProjectileCombat] Damage set to {damage}");
        }

        public float GetDamage()
        {
            return damageAmount;
        }

        public void SetDamageMultiplier(float multiplier)
        {
            damageMultiplier = multiplier;
            Debug.Log($"[ProjectileCombat] Damage multiplier set to {multiplier}");
        }

        public float GetDamageMultiplier()
        {
            return damageMultiplier;
        }

        public bool CanDamage(IDamageable target)
        {
            // TODO: Implement damage validation logic in Phase 2
            return target != null && target.IsVulnerable;
        }

        public void SetCollisionLayers(LayerMask layers)
        {
            collisionLayers = layers;
            Debug.Log($"[ProjectileCombat] Collision layers set to {layers.value}");
        }

        public LayerMask GetCollisionLayers()
        {
            return collisionLayers;
        }
        #endregion

        #region Unity Collision Events
        private void OnCollisionEnter(Collision collision)
        {
            HandleCollision(collision);
        }

        private void OnTriggerEnter(Collider other)
        {
            HandleTrigger(other);
        }
        #endregion
    }
}
