using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile visual and audio effects.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileEffects : MonoBehaviour, IProjectileEffects
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;
        
        // Placeholder fields - will be populated in Phase 2
        [SerializeField] private bool trailEnabled = true;
        [SerializeField] private bool particlesEnabled = true;
        [SerializeField] private Color currentColor = Color.white;
        [SerializeField] private float currentScale = 1f;
        
        // Cached components
        private TrailRenderer trailRenderer;
        private ParticleSystem[] particleSystems;
        private Renderer modelRenderer;
        private LineRenderer lineRenderer;
        #endregion

        #region IProjectileEffects Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;
            
            // Cache effect components
            CacheEffectComponents();
            
            isInitialized = true;
            
            Debug.Log($"[ProjectileEffects] Initialized for projectile {projectile.EntityID}");
        }

        public void OnSpawn()
        {
            if (!isInitialized) return;
            
            // TODO: Migrate spawn effects logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileEffects] Spawn effects triggered");
        }

        public void OnUpdate()
        {
            if (!isInitialized) return;
            
            // TODO: Migrate update effects logic from ProjectileStateBased in Phase 2
        }

        public void OnHit(Vector3 hitPoint, Vector3 normal)
        {
            if (!isInitialized) return;
            
            // TODO: Migrate hit effects logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileEffects] Hit effects at {hitPoint}");
        }

        public void OnDestroy()
        {
            if (!isInitialized) return;
            
            // TODO: Migrate destruction effects logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileEffects] Destroy effects triggered");
        }

        public void OnVelocityChanged(Vector3 velocity)
        {
            if (!isInitialized) return;
            
            // TODO: Update velocity-based effects in Phase 2
        }

        public void SetTrailEnabled(bool enabled)
        {
            trailEnabled = enabled;
            if (trailRenderer != null)
            {
                trailRenderer.enabled = enabled;
            }
            Debug.Log($"[ProjectileEffects] Trail {(enabled ? "enabled" : "disabled")}");
        }

        public bool IsTrailEnabled()
        {
            return trailEnabled;
        }

        public void SetTrailMaterial(Material material)
        {
            if (trailRenderer != null && material != null)
            {
                trailRenderer.material = material;
                Debug.Log($"[ProjectileEffects] Trail material set to {material.name}");
            }
        }

        public void SetParticlesEnabled(bool enabled)
        {
            particlesEnabled = enabled;
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null)
                    {
                        if (enabled)
                            ps.Play();
                        else
                            ps.Stop();
                    }
                }
            }
            Debug.Log($"[ProjectileEffects] Particles {(enabled ? "enabled" : "disabled")}");
        }

        public bool IsParticlesEnabled()
        {
            return particlesEnabled;
        }

        public void SetColor(Color color)
        {
            currentColor = color;
            
            // TODO: Apply color to materials in Phase 2
            Debug.Log($"[ProjectileEffects] Color set to {color}");
        }

        public Color GetColor()
        {
            return currentColor;
        }

        public void SetScale(float scale)
        {
            currentScale = scale;
            transform.localScale = Vector3.one * scale;
            Debug.Log($"[ProjectileEffects] Scale set to {scale}");
        }

        public float GetScale()
        {
            return currentScale;
        }

        public void CleanupEffects()
        {
            // TODO: Migrate cleanup logic from ProjectileStateBased in Phase 2
            if (trailRenderer != null)
            {
                trailRenderer.Clear();
            }
            
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null)
                    {
                        ps.Stop();
                        ps.Clear();
                    }
                }
            }
            
            Debug.Log($"[ProjectileEffects] Effects cleaned up");
        }

        public void ResetForPool()
        {
            currentColor = Color.white;
            currentScale = 1f;
            trailEnabled = true;
            particlesEnabled = true;
            
            CleanupEffects();
            
            Debug.Log($"[ProjectileEffects] Reset for pool");
        }
        #endregion

        #region Private Methods
        private void CacheEffectComponents()
        {
            trailRenderer = GetComponent<TrailRenderer>();
            particleSystems = GetComponentsInChildren<ParticleSystem>();
            lineRenderer = GetComponent<LineRenderer>();
            
            // Find the Projectile Model child object's renderer
            var projectileModel = transform.Find("Projectile Model");
            if (projectileModel != null)
            {
                modelRenderer = projectileModel.GetComponent<Renderer>();
            }
            else
            {
                // Fallback to any renderer on this object
                modelRenderer = GetComponent<Renderer>();
            }
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            CacheEffectComponents();
        }
        #endregion
    }
}
