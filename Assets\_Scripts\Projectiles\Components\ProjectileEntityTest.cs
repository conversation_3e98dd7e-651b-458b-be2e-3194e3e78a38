using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Simple test script to validate Phase 1 component foundation.
    /// This script can be attached to a GameObject to test ProjectileEntity functionality.
    /// Remove this file after Phase 1 validation is complete.
    /// </summary>
    public class ProjectileEntityTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private float testDamage = 25f;
        [SerializeField] private float testSpeed = 50f;
        [SerializeField] private float testLifetime = 3f;
        [SerializeField] private bool testHoming = true;
        [SerializeField] private float testScale = 1.5f;
        [SerializeField] private Transform testTarget;

        private ProjectileEntity projectileEntity;

        private void Start()
        {
            if (runTestOnStart)
            {
                RunComponentFoundationTest();
            }
        }

        [ContextMenu("Run Component Foundation Test")]
        public void RunComponentFoundationTest()
        {
            Debug.Log("=== PHASE 1 COMPONENT FOUNDATION TEST ===");

            // Test 1: Component Discovery
            TestComponentDiscovery();

            // Test 2: Entity Initialization
            TestEntityInitialization();

            // Test 3: Component Initialization
            TestComponentInitialization();

            // Test 4: Setup Projectile
            TestSetupProjectile();

            // Test 5: Component Communication
            TestComponentCommunication();

            Debug.Log("=== PHASE 1 TEST COMPLETED ===");
        }

        private void TestComponentDiscovery()
        {
            Debug.Log("--- Test 1: Component Discovery ---");

            projectileEntity = GetComponent<ProjectileEntity>();
            if (projectileEntity == null)
            {
                projectileEntity = gameObject.AddComponent<ProjectileEntity>();
            }

            // Add component classes if they don't exist
            if (GetComponent<ProjectileMovement>() == null)
                gameObject.AddComponent<ProjectileMovement>();
            if (GetComponent<ProjectileCombat>() == null)
                gameObject.AddComponent<ProjectileCombat>();
            if (GetComponent<ProjectileLifecycle>() == null)
                gameObject.AddComponent<ProjectileLifecycle>();
            if (GetComponent<ProjectileEffects>() == null)
                gameObject.AddComponent<ProjectileEffects>();

            // Add required Unity components
            if (GetComponent<Rigidbody>() == null)
            {
                var rb = gameObject.AddComponent<Rigidbody>();
                rb.useGravity = false;
                rb.isKinematic = true;
            }

            if (GetComponent<SphereCollider>() == null)
            {
                var collider = gameObject.AddComponent<SphereCollider>();
                collider.isTrigger = true;
                collider.radius = 0.5f;
            }

            Debug.Log("✅ Component discovery setup complete");
        }

        private void TestEntityInitialization()
        {
            Debug.Log("--- Test 2: Entity Initialization ---");

            if (projectileEntity == null)
            {
                Debug.LogError("❌ ProjectileEntity not found");
                return;
            }

            // Test IEntity properties
            Debug.Log($"Entity ID: {projectileEntity.EntityID}");
            Debug.Log($"GameObject: {projectileEntity.GameObject.name}");
            Debug.Log($"Transform: {projectileEntity.Transform.name}");
            Debug.Log($"Is Initialized: {projectileEntity.IsInitialized}");
            Debug.Log($"Is Active: {projectileEntity.IsActive}");

            // Test ICombatEntity properties
            Debug.Log($"Health: {projectileEntity.Health}");
            Debug.Log($"Max Health: {projectileEntity.MaxHealth}");
            Debug.Log($"Is Vulnerable: {projectileEntity.IsVulnerable}");
            Debug.Log($"Is Alive: {projectileEntity.IsAlive}");

            Debug.Log("✅ Entity initialization test complete");
        }

        private void TestComponentInitialization()
        {
            Debug.Log("--- Test 3: Component Initialization ---");

            // Test component access
            bool movementOk = projectileEntity.Movement != null;
            bool combatOk = projectileEntity.Combat != null;
            bool lifecycleOk = projectileEntity.Lifecycle != null;
            bool effectsOk = projectileEntity.Effects != null;

            Debug.Log($"Movement Component: {(movementOk ? "✅" : "❌")}");
            Debug.Log($"Combat Component: {(combatOk ? "✅" : "❌")}");
            Debug.Log($"Lifecycle Component: {(lifecycleOk ? "✅" : "❌")}");
            Debug.Log($"Effects Component: {(effectsOk ? "✅" : "❌")}");

            if (movementOk && combatOk && lifecycleOk && effectsOk)
            {
                Debug.Log("✅ All components initialized successfully");
            }
            else
            {
                Debug.LogError("❌ Some components failed to initialize");
            }
        }

        private void TestSetupProjectile()
        {
            Debug.Log("--- Test 4: Setup Projectile ---");

            projectileEntity.SetupProjectile(
                testDamage,
                testSpeed,
                testLifetime,
                testHoming,
                testScale,
                testTarget
            );

            // Verify properties were set
            Debug.Log($"Damage Amount: {projectileEntity.DamageAmount} (expected: {testDamage})");
            Debug.Log($"Bullet Speed: {projectileEntity.BulletSpeed} (expected: {testSpeed})");
            Debug.Log($"Lifetime: {projectileEntity.Lifetime} (expected: {testLifetime})");
            Debug.Log($"Homing: {projectileEntity.Homing} (expected: {testHoming})");
            Debug.Log($"Current Target: {(projectileEntity.CurrentTarget != null ? projectileEntity.CurrentTarget.name : "None")}");

            Debug.Log("✅ Setup projectile test complete");
        }

        private void TestComponentCommunication()
        {
            Debug.Log("--- Test 5: Component Communication ---");

            // Test movement component
            if (projectileEntity.Movement != null)
            {
                Debug.Log($"Movement Speed: {projectileEntity.Movement.GetSpeed()}");
                Debug.Log($"Movement Target: {(projectileEntity.Movement.GetTarget() != null ? projectileEntity.Movement.GetTarget().name : "None")}");
                Debug.Log($"Is Homing: {projectileEntity.Movement.IsHoming()}");
            }

            // Test combat component
            if (projectileEntity.Combat != null)
            {
                Debug.Log($"Combat Damage: {projectileEntity.Combat.GetDamage()}");
                Debug.Log($"Damage Multiplier: {projectileEntity.Combat.GetDamageMultiplier()}");
            }

            // Test lifecycle component
            if (projectileEntity.Lifecycle != null)
            {
                Debug.Log($"Total Lifetime: {projectileEntity.Lifecycle.GetTotalLifetime()}");
                Debug.Log($"Remaining Lifetime: {projectileEntity.Lifecycle.GetRemainingLifetime()}");
                Debug.Log($"Time Alive: {projectileEntity.Lifecycle.GetTimeAlive()}");
            }

            // Test effects component
            if (projectileEntity.Effects != null)
            {
                Debug.Log($"Trail Enabled: {projectileEntity.Effects.IsTrailEnabled()}");
                Debug.Log($"Particles Enabled: {projectileEntity.Effects.IsParticlesEnabled()}");
                Debug.Log($"Current Scale: {projectileEntity.Effects.GetScale()}");
            }

            Debug.Log("✅ Component communication test complete");
        }

        private void Update()
        {
            // Simple test to verify Update calls work
            if (projectileEntity != null && projectileEntity.IsActive)
            {
                // This will call UpdateComponents() which calls component Update methods
            }
        }

        private void FixedUpdate()
        {
            // Simple test to verify FixedUpdate calls work
            if (projectileEntity != null && projectileEntity.IsActive)
            {
                // This will call FixedUpdateComponents() which calls component FixedUpdate methods
            }
        }
    }
}
