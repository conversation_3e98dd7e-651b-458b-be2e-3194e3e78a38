using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile lifecycle management.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileLifecycle : MonoBehaviour, IProjectileLifecycle
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;
        
        // Placeholder fields - will be populated in Phase 2
        [SerializeField] private float lifetime = 5f;
        [SerializeField] private float maxDistance = 100f;
        [SerializeField] private bool isLifetimePaused = false;
        
        private float currentLifetime;
        private float timeAlive = 0f;
        private Vector3 initialPosition;
        private float creationTime;
        #endregion

        #region IProjectileLifecycle Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;
            currentLifetime = lifetime;
            timeAlive = 0f;
            initialPosition = transform.position;
            creationTime = Time.time;
            isInitialized = true;
            
            Debug.Log($"[ProjectileLifecycle] Initialized for projectile {projectile.EntityID} with lifetime {lifetime}");
        }

        public void UpdateLifecycle(float deltaTime)
        {
            if (!isInitialized || isLifetimePaused) return;
            
            // Update time alive
            timeAlive += deltaTime;
            
            // Update remaining lifetime
            currentLifetime -= deltaTime;
            
            // TODO: Migrate additional lifecycle logic from ProjectileStateBased in Phase 2
        }

        public bool ShouldDestroy()
        {
            if (!isInitialized) return false;
            
            // Check lifetime
            if (currentLifetime <= 0f)
            {
                Debug.Log($"[ProjectileLifecycle] Projectile should destroy - lifetime expired");
                return true;
            }
            
            // Check max distance
            if (maxDistance > 0f)
            {
                float distanceTraveled = Vector3.Distance(transform.position, initialPosition);
                if (distanceTraveled >= maxDistance)
                {
                    Debug.Log($"[ProjectileLifecycle] Projectile should destroy - max distance reached");
                    return true;
                }
            }
            
            // TODO: Add additional destruction conditions in Phase 2
            return false;
        }

        public void OnDestroy()
        {
            // TODO: Migrate destruction logic from ProjectileStateBased in Phase 2
            Debug.Log($"[ProjectileLifecycle] Projectile destroyed after {timeAlive} seconds");
        }

        public void SetLifetime(float lifetime)
        {
            this.lifetime = lifetime;
            this.currentLifetime = lifetime;
            Debug.Log($"[ProjectileLifecycle] Lifetime set to {lifetime}");
        }

        public float GetRemainingLifetime()
        {
            return currentLifetime;
        }

        public float GetTotalLifetime()
        {
            return lifetime;
        }

        public float GetTimeAlive()
        {
            return timeAlive;
        }

        public void SetLifetimePaused(bool paused)
        {
            isLifetimePaused = paused;
            Debug.Log($"[ProjectileLifecycle] Lifetime {(paused ? "paused" : "resumed")}");
        }

        public bool IsLifetimePaused()
        {
            return isLifetimePaused;
        }

        public void ExtendLifetime(float extension)
        {
            currentLifetime += extension;
            lifetime += extension;
            Debug.Log($"[ProjectileLifecycle] Lifetime extended by {extension} seconds");
        }

        public void SetMaxDistance(float maxDistance)
        {
            this.maxDistance = maxDistance;
            Debug.Log($"[ProjectileLifecycle] Max distance set to {maxDistance}");
        }

        public float GetMaxDistance()
        {
            return maxDistance;
        }

        public void ForceDestroy(bool hitTarget = false)
        {
            currentLifetime = 0f;
            Debug.Log($"[ProjectileLifecycle] Force destroy called - hit target: {hitTarget}");
        }

        public void ResetForPool()
        {
            timeAlive = 0f;
            currentLifetime = lifetime;
            initialPosition = transform.position;
            creationTime = Time.time;
            isLifetimePaused = false;
            
            Debug.Log($"[ProjectileLifecycle] Reset for pool");
        }
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (isInitialized)
            {
                initialPosition = transform.position;
                creationTime = Time.time;
            }
        }
        #endregion

        #region Debug Methods
        private void OnDrawGizmosSelected()
        {
            if (maxDistance > 0f)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(initialPosition, maxDistance);
            }
        }
        #endregion
    }
}
