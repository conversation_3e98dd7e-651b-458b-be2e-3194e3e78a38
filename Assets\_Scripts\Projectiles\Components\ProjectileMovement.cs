using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile movement logic.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileMovement : MonoBehaviour, IProjectileMovement
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;
        
        // Placeholder fields - will be populated in Phase 2
        [SerializeField] private float bulletSpeed = 25f;
        [SerializeField] private float turnRate = 5f;
        [SerializeField] private bool homing = false;
        [SerializeField] private Transform currentTarget;
        
        private Rigidbody rb;
        private float distanceTraveled = 0f;
        private Vector3 lastPosition;
        #endregion

        #region IProjectileMovement Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;
            rb = GetComponent<Rigidbody>();
            lastPosition = transform.position;
            isInitialized = true;
            
            Debug.Log($"[ProjectileMovement] Initialized for projectile {projectile.EntityID}");
        }

        public void UpdateMovement(float deltaTime)
        {
            if (!isInitialized) return;
            
            // TODO: Migrate movement logic from ProjectileStateBased in Phase 2
            // For now, just track distance traveled
            if (lastPosition != Vector3.zero)
            {
                distanceTraveled += Vector3.Distance(transform.position, lastPosition);
            }
            lastPosition = transform.position;
        }

        public Vector3 GetCurrentVelocity()
        {
            // TODO: Implement in Phase 2
            return rb != null ? rb.linearVelocity : Vector3.zero;
        }

        public bool IsMovementComplete()
        {
            // TODO: Implement in Phase 2
            return false;
        }

        public void EnableHoming(bool enable)
        {
            homing = enable;
            // TODO: Implement homing logic in Phase 2
            Debug.Log($"[ProjectileMovement] Homing {(enable ? "enabled" : "disabled")}");
        }

        public void SetTarget(Transform target)
        {
            currentTarget = target;
            // TODO: Implement target tracking in Phase 2
            Debug.Log($"[ProjectileMovement] Target set to {(target != null ? target.name : "None")}");
        }

        public Transform GetTarget()
        {
            return currentTarget;
        }

        public void SetSpeed(float speed)
        {
            bulletSpeed = speed;
            // TODO: Apply speed to movement in Phase 2
            Debug.Log($"[ProjectileMovement] Speed set to {speed}");
        }

        public float GetSpeed()
        {
            return bulletSpeed;
        }

        public float GetDistanceTraveled()
        {
            return distanceTraveled;
        }

        public bool IsHoming()
        {
            return homing && currentTarget != null;
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
        }

        private void Start()
        {
            lastPosition = transform.position;
        }
        #endregion

        #region Debug Methods
        private void OnDrawGizmosSelected()
        {
            if (currentTarget != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, currentTarget.position);
                Gizmos.DrawWireSphere(currentTarget.position, 1f);
            }
        }
        #endregion
    }
}
