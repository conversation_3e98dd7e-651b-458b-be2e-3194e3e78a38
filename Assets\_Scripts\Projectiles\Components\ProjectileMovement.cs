using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile movement logic.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileMovement : MonoBehaviour, IProjectileMovement
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;

        // Movement configuration
        [SerializeField] private float bulletSpeed = 25f;
        [SerializeField] private float turnRate = 5f;
        [SerializeField] private bool homing = false;
        [SerializeField] private Transform currentTarget;
        [SerializeField] private float maxTurnRate = 360f;
        [SerializeField] private float homingStrength = 1f;

        // Physics and tracking
        private Rigidbody rb;
        private float distanceTraveled = 0f;
        private Vector3 lastPosition;
        private Vector3 lastTargetDirection;
        private Vector3 lastSteeringForce;

        // Performance optimization
        private Transform cachedTransform;
        private bool hasValidTarget = false;
        #endregion

        #region IProjectileMovement Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;

            // Cache components
            rb = GetComponent<Rigidbody>();
            cachedTransform = transform;
            lastPosition = cachedTransform.position;

            // Initialize physics
            if (rb != null)
            {
                rb.isKinematic = false;
                rb.useGravity = false;
                rb.linearDamping = 0f;
                rb.angularDamping = 0f;
            }

            isInitialized = true;
            Debug.Log($"[ProjectileMovement] Initialized for projectile {projectile.EntityID}");
        }

        public void UpdateMovement(float deltaTime)
        {
            if (!isInitialized || rb == null) return;

            // Update distance tracking
            Vector3 currentPosition = cachedTransform.position;
            distanceTraveled += Vector3.Distance(lastPosition, currentPosition);
            lastPosition = currentPosition;

            // Calculate movement
            Vector3 targetVelocity = CalculateTargetVelocity(deltaTime);

            // Apply movement
            rb.linearVelocity = targetVelocity;

            // Update rotation if homing
            if (homing && hasValidTarget)
            {
                UpdateRotation(deltaTime);
            }
        }

        public Vector3 GetCurrentVelocity()
        {
            // TODO: Implement in Phase 2
            return rb != null ? rb.linearVelocity : Vector3.zero;
        }

        public bool IsMovementComplete()
        {
            // TODO: Implement in Phase 2
            return false;
        }

        public void EnableHoming(bool enable)
        {
            homing = enable;
            hasValidTarget = enable && currentTarget != null;
            Debug.Log($"[ProjectileMovement] Homing {(enable ? "enabled" : "disabled")}");
        }

        public void SetTarget(Transform target)
        {
            currentTarget = target;
            hasValidTarget = homing && target != null;
            if (hasValidTarget)
            {
                lastTargetDirection = (target.position - cachedTransform.position).normalized;
            }
            Debug.Log($"[ProjectileMovement] Target set to {(target != null ? target.name : "None")}");
        }

        public Transform GetTarget()
        {
            return currentTarget;
        }

        public void SetSpeed(float speed)
        {
            bulletSpeed = speed;
            // TODO: Apply speed to movement in Phase 2
            Debug.Log($"[ProjectileMovement] Speed set to {speed}");
        }

        public float GetSpeed()
        {
            return bulletSpeed;
        }

        public float GetDistanceTraveled()
        {
            return distanceTraveled;
        }

        public bool IsHoming()
        {
            return homing && currentTarget != null;
        }
        #endregion

        #region Private Movement Methods
        private Vector3 CalculateTargetVelocity(float deltaTime)
        {
            Vector3 baseVelocity = cachedTransform.forward * bulletSpeed;

            if (!homing || currentTarget == null)
            {
                hasValidTarget = false;
                return baseVelocity;
            }

            hasValidTarget = true;

            // Calculate direction to target
            Vector3 directionToTarget = (currentTarget.position - cachedTransform.position).normalized;

            // Apply steering force for homing
            Vector3 steeringForce = directionToTarget * homingStrength;

            // Smooth the steering to prevent jittery movement
            steeringForce = Vector3.Lerp(lastSteeringForce, steeringForce, deltaTime * turnRate);
            lastSteeringForce = steeringForce;

            // Combine base velocity with steering
            Vector3 targetVelocity = (baseVelocity + steeringForce).normalized * bulletSpeed;

            return targetVelocity;
        }

        private void UpdateRotation(float deltaTime)
        {
            if (currentTarget == null) return;

            Vector3 targetDirection = (currentTarget.position - cachedTransform.position).normalized;

            // Smooth rotation towards target
            Vector3 smoothedDirection = Vector3.Slerp(lastTargetDirection, targetDirection, deltaTime * turnRate);
            lastTargetDirection = smoothedDirection;

            // Apply rotation with max turn rate limit
            float maxRotationThisFrame = maxTurnRate * deltaTime;
            Vector3 newForward = Vector3.RotateTowards(cachedTransform.forward, smoothedDirection,
                                                      Mathf.Deg2Rad * maxRotationThisFrame, 0f);

            cachedTransform.rotation = Quaternion.LookRotation(newForward);
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
        }

        private void Start()
        {
            lastPosition = transform.position;
        }
        #endregion

        #region Debug Methods
        private void OnDrawGizmosSelected()
        {
            if (currentTarget != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, currentTarget.position);
                Gizmos.DrawWireSphere(currentTarget.position, 1f);
            }
        }
        #endregion
    }
}
