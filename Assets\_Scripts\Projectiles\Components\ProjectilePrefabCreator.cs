#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Chronos;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Editor utility to automatically create a test projectile prefab with the new component system.
    /// This helps ensure the prefab is set up correctly for Phase 1 testing.
    /// </summary>
    public class ProjectilePrefabCreator : EditorWindow
    {
        [Header("Prefab Configuration")]
        public string prefabName = "TestProjectileEntity";
        public string prefabPath = "Assets/_Prefabs/";

        [Header("Visual Configuration")]
        public Mesh projectileMesh;
        public Material projectileMaterial;

        [Header("Physics Configuration")]
        public float colliderRadius = 0.5f;
        public bool useGravity = false;
        public bool isKinematic = true;

        [Header("Test Configuration")]
        public bool includeTestComponent = true;
        public bool includeTrailRenderer = false;
        public bool includeParticleSystem = false;

        [MenuItem("BTR/Projectiles/Create Test Projectile Prefab")]
        public static void ShowWindow()
        {
            GetWindow<ProjectilePrefabCreator>("Projectile Prefab Creator");
        }

        private void OnGUI()
        {
            GUILayout.Label("Create Test Projectile Prefab", EditorStyles.boldLabel);
            GUILayout.Space(10);

            // Prefab Configuration
            GUILayout.Label("Prefab Configuration", EditorStyles.boldLabel);
            prefabName = EditorGUILayout.TextField("Prefab Name", prefabName);
            prefabPath = EditorGUILayout.TextField("Prefab Path", prefabPath);
            GUILayout.Space(10);

            // Visual Configuration
            GUILayout.Label("Visual Configuration", EditorStyles.boldLabel);
            projectileMesh = (Mesh)EditorGUILayout.ObjectField("Projectile Mesh", projectileMesh, typeof(Mesh), false);
            projectileMaterial = (Material)EditorGUILayout.ObjectField("Projectile Material", projectileMaterial, typeof(Material), false);
            GUILayout.Space(10);

            // Physics Configuration
            GUILayout.Label("Physics Configuration", EditorStyles.boldLabel);
            colliderRadius = EditorGUILayout.FloatField("Collider Radius", colliderRadius);
            useGravity = EditorGUILayout.Toggle("Use Gravity", useGravity);
            isKinematic = EditorGUILayout.Toggle("Is Kinematic", isKinematic);
            GUILayout.Space(10);

            // Test Configuration
            GUILayout.Label("Test Configuration", EditorStyles.boldLabel);
            includeTestComponent = EditorGUILayout.Toggle("Include Test Component", includeTestComponent);
            includeTrailRenderer = EditorGUILayout.Toggle("Include Trail Renderer", includeTrailRenderer);
            includeParticleSystem = EditorGUILayout.Toggle("Include Particle System", includeParticleSystem);
            GUILayout.Space(20);

            // Create Button
            if (GUILayout.Button("Create Test Projectile Prefab", GUILayout.Height(30)))
            {
                CreateTestProjectilePrefab();
            }

            GUILayout.Space(10);

            // Help Text
            EditorGUILayout.HelpBox(
                "This will create a test projectile prefab with the new component system. " +
                "The prefab will include all required components for Phase 1 testing. " +
                "The new prefab will work with the ProjectileSystemBridge for unified management. " +
                "If no mesh/material is specified, default primitives will be used.",
                MessageType.Info
            );

            EditorGUILayout.HelpBox(
                "Integration Status: The management system bridge is now available. " +
                "New prefabs will automatically work with both legacy and new management systems " +
                "through the IProjectile interface.",
                MessageType.Info
            );
        }

        private void CreateTestProjectilePrefab()
        {
            try
            {
                // Create root GameObject
                GameObject rootObject = new GameObject(prefabName);

                // Add Unity components
                AddUnityComponents(rootObject);

                // Add new component system
                AddProjectileComponents(rootObject);

                // Create visual model child
                CreateVisualModel(rootObject);

                // Add optional components
                if (includeTrailRenderer)
                    AddTrailRenderer(rootObject);

                if (includeParticleSystem)
                    CreateParticleSystem(rootObject);

                // Create prefab
                string fullPath = System.IO.Path.Combine(prefabPath, prefabName + ".prefab");

                // Ensure directory exists
                System.IO.Directory.CreateDirectory(prefabPath);

                // Create prefab asset
                GameObject prefab = PrefabUtility.SaveAsPrefabAsset(rootObject, fullPath);

                // Clean up scene object
                DestroyImmediate(rootObject);

                // Select the created prefab
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);

                Debug.Log($"[ProjectilePrefabCreator] Successfully created test projectile prefab: {fullPath}");

                // Show success message
                EditorUtility.DisplayDialog("Success",
                    $"Test projectile prefab created successfully!\n\nLocation: {fullPath}\n\n" +
                    "The prefab is ready for Phase 1 testing. Place it in a scene and check the console for test results.",
                    "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePrefabCreator] Failed to create prefab: {e.Message}");
                EditorUtility.DisplayDialog("Error",
                    $"Failed to create prefab:\n{e.Message}",
                    "OK");
            }
        }

        private void AddUnityComponents(GameObject rootObject)
        {
            // Add Rigidbody
            Rigidbody rb = rootObject.AddComponent<Rigidbody>();
            rb.useGravity = useGravity;
            rb.isKinematic = isKinematic;
            rb.interpolation = RigidbodyInterpolation.Interpolate;
            rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;

            // Add SphereCollider
            SphereCollider collider = rootObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = colliderRadius;

            // Add Timeline (Chronos)
            Timeline timeline = rootObject.AddComponent<Timeline>();
            timeline.enabled = true;
            timeline.rewindable = true;
        }

        private void AddProjectileComponents(GameObject rootObject)
        {
            // Ensure we don't have conflicting old system components
            var existingInitializer = rootObject.GetComponent<ProjectileInitializer>();
            if (existingInitializer != null)
            {
                Debug.LogWarning("[ProjectilePrefabCreator] Removing ProjectileInitializer - not needed for new component system");
                DestroyImmediate(existingInitializer);
            }

            var existingStateBased = rootObject.GetComponent<ProjectileStateBased>();
            if (existingStateBased != null)
            {
                Debug.LogWarning("[ProjectilePrefabCreator] Removing ProjectileStateBased - replaced by new component system");
                DestroyImmediate(existingStateBased);
            }

            // Add ProjectileEntity (this will auto-discover other components)
            rootObject.AddComponent<ProjectileEntity>();

            // Add component classes
            rootObject.AddComponent<ProjectileMovement>();
            rootObject.AddComponent<ProjectileCombat>();
            rootObject.AddComponent<ProjectileLifecycle>();
            rootObject.AddComponent<ProjectileEffects>();

            // Add test component if requested
            if (includeTestComponent)
            {
                rootObject.AddComponent<ProjectileEntityTest>();
            }

            Debug.Log("[ProjectilePrefabCreator] Added new component system - ProjectileEntity + 4 component classes");
        }

        private void CreateVisualModel(GameObject rootObject)
        {
            // Create child GameObject for visual model
            GameObject modelChild = new GameObject("Projectile Model");
            modelChild.transform.SetParent(rootObject.transform);
            modelChild.transform.localPosition = Vector3.zero;
            modelChild.transform.localRotation = Quaternion.identity;
            modelChild.transform.localScale = Vector3.one;

            // Add MeshRenderer and MeshFilter
            MeshRenderer renderer = modelChild.AddComponent<MeshRenderer>();
            MeshFilter meshFilter = modelChild.AddComponent<MeshFilter>();

            // Set mesh (use sphere primitive if none specified)
            if (projectileMesh != null)
            {
                meshFilter.mesh = projectileMesh;
            }
            else
            {
                // Create a simple sphere mesh
                GameObject tempSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                meshFilter.mesh = tempSphere.GetComponent<MeshFilter>().sharedMesh;
                DestroyImmediate(tempSphere);
            }

            // Set material (use default if none specified)
            if (projectileMaterial != null)
            {
                renderer.material = projectileMaterial;
            }
            else
            {
                // Create a simple colored material
                Material defaultMat = new Material(Shader.Find("Standard"));
                defaultMat.color = Color.cyan;
                defaultMat.name = "TestProjectileMaterial";
                renderer.material = defaultMat;
            }
        }

        private void AddTrailRenderer(GameObject rootObject)
        {
            TrailRenderer trail = rootObject.AddComponent<TrailRenderer>();
            trail.time = 0.5f;
            trail.startWidth = 0.2f;
            trail.endWidth = 0.05f;
            trail.material = projectileMaterial ?? new Material(Shader.Find("Sprites/Default"));
        }

        private void CreateParticleSystem(GameObject rootObject)
        {
            GameObject particleChild = new GameObject("Projectile Movement Particles");
            particleChild.transform.SetParent(rootObject.transform);
            particleChild.transform.localPosition = Vector3.zero;

            ParticleSystem particles = particleChild.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1f;
            main.startSpeed = 2f;
            main.startSize = 0.1f;
            main.maxParticles = 50;
        }
    }
}
#endif
