using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// Common interface for both ProjectileStateBased (legacy) and ProjectileEntity (new) systems.
    /// This allows the management infrastructure to work with both systems during the transition.
    /// </summary>
    public interface IProjectile
    {
        #region Core Properties
        /// <summary>
        /// The GameObject this projectile is attached to
        /// </summary>
        GameObject GameObject { get; }
        
        /// <summary>
        /// The Transform component of this projectile
        /// </summary>
        Transform Transform { get; }
        
        /// <summary>
        /// Unique instance ID for this projectile
        /// </summary>
        int InstanceID { get; }
        
        /// <summary>
        /// Whether this projectile is currently active
        /// </summary>
        bool IsActive { get; }
        #endregion

        #region Movement Properties
        /// <summary>
        /// Current movement speed
        /// </summary>
        float BulletSpeed { get; set; }
        
        /// <summary>
        /// Whether homing is enabled
        /// </summary>
        bool Homing { get; set; }
        
        /// <summary>
        /// Current target for homing projectiles
        /// </summary>
        Transform CurrentTarget { get; set; }
        
        /// <summary>
        /// Job system index for performance integration
        /// </summary>
        int ProjectileIndex { get; set; }
        #endregion

        #region Combat Properties
        /// <summary>
        /// Damage amount this projectile deals
        /// </summary>
        float DamageAmount { get; set; }
        
        /// <summary>
        /// Whether this is a player-fired projectile
        /// </summary>
        bool IsPlayerShot { get; set; }
        #endregion

        #region Lifecycle Properties
        /// <summary>
        /// Remaining lifetime in seconds
        /// </summary>
        float Lifetime { get; set; }
        
        /// <summary>
        /// Whether the projectile has hit its target
        /// </summary>
        bool HasHitTarget { get; set; }
        #endregion

        #region Component Access
        /// <summary>
        /// Rigidbody component for physics
        /// </summary>
        Rigidbody Rigidbody { get; }
        
        /// <summary>
        /// Timeline component for Chronos integration
        /// </summary>
        Chronos.Timeline Timeline { get; }
        #endregion

        #region Lifecycle Methods
        /// <summary>
        /// Setup projectile with initial parameters
        /// </summary>
        void SetupProjectile(float damage, float speed, float lifetime, bool enableHoming, float scale, Transform target);
        
        /// <summary>
        /// Enable or disable homing behavior
        /// </summary>
        void EnableHoming(bool enable);
        
        /// <summary>
        /// Handle projectile death/destruction
        /// </summary>
        void Death(bool hitTarget = false);
        
        /// <summary>
        /// Reset projectile for pooling
        /// </summary>
        void ResetForPool();
        #endregion

        #region Audio Integration
        /// <summary>
        /// Play projectile sound effect
        /// </summary>
        void PlaySound();
        #endregion

        #region System Integration
        /// <summary>
        /// Get the projectile type for system identification
        /// </summary>
        ProjectileSystemType GetProjectileSystemType();
        #endregion
    }

    /// <summary>
    /// Enum to identify which projectile system is being used
    /// </summary>
    public enum ProjectileSystemType
    {
        Legacy,     // ProjectileStateBased
        Component   // ProjectileEntity
    }
}
