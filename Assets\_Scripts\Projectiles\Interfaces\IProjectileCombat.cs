using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// Interface for projectile combat components.
    /// Handles damage calculation, collision detection, and combat-related functionality.
    /// </summary>
    public interface IProjectileCombat
    {
        /// <summary>
        /// Initialize the combat component with the parent projectile entity
        /// </summary>
        /// <param name="projectile">The projectile entity this component belongs to</param>
        void Initialize(ProjectileEntity projectile);
        
        /// <summary>
        /// Apply damage to a target
        /// </summary>
        /// <param name="target">Target that implements IDamageable</param>
        /// <param name="hitPoint">World position where the hit occurred</param>
        void ApplyDamage(IDamageable target, Vector3 hitPoint);
        
        /// <summary>
        /// Calculate damage amount for a specific target
        /// </summary>
        /// <param name="target">Target to calculate damage for</param>
        /// <returns>Final damage amount after all modifiers</returns>
        float CalculateDamage(IDamageable target);
        
        /// <summary>
        /// Handle collision events
        /// </summary>
        /// <param name="collision">Collision data from Unity</param>
        void HandleCollision(Collision collision);
        
        /// <summary>
        /// Handle trigger events
        /// </summary>
        /// <param name="other">Collider that triggered the event</param>
        void HandleTrigger(Collider other);
        
        /// <summary>
        /// Set base damage amount
        /// </summary>
        /// <param name="damage">Base damage value</param>
        void SetDamage(float damage);
        
        /// <summary>
        /// Get current damage amount
        /// </summary>
        /// <returns>Current damage value</returns>
        float GetDamage();
        
        /// <summary>
        /// Set damage multiplier
        /// </summary>
        /// <param name="multiplier">Damage multiplier (1.0 = normal damage)</param>
        void SetDamageMultiplier(float multiplier);
        
        /// <summary>
        /// Get current damage multiplier
        /// </summary>
        /// <returns>Current damage multiplier</returns>
        float GetDamageMultiplier();
        
        /// <summary>
        /// Check if projectile can damage a specific target
        /// </summary>
        /// <param name="target">Target to check</param>
        /// <returns>True if target can be damaged</returns>
        bool CanDamage(IDamageable target);
        
        /// <summary>
        /// Set collision layers that this projectile can interact with
        /// </summary>
        /// <param name="layers">LayerMask for collision detection</param>
        void SetCollisionLayers(LayerMask layers);
        
        /// <summary>
        /// Get current collision layers
        /// </summary>
        /// <returns>Current collision LayerMask</returns>
        LayerMask GetCollisionLayers();
    }
}
