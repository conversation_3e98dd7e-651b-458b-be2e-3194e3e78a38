using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// Interface for projectile effects components.
    /// Handles visual effects, audio, trails, and other presentation elements.
    /// </summary>
    public interface IProjectileEffects
    {
        /// <summary>
        /// Initialize the effects component with the parent projectile entity
        /// </summary>
        /// <param name="projectile">The projectile entity this component belongs to</param>
        void Initialize(ProjectileEntity projectile);
        
        /// <summary>
        /// Called when projectile is spawned
        /// </summary>
        void OnSpawn();
        
        /// <summary>
        /// Update effects - called from Update
        /// </summary>
        void OnUpdate();
        
        /// <summary>
        /// Called when projectile hits something
        /// </summary>
        /// <param name="hitPoint">World position of the hit</param>
        /// <param name="normal">Surface normal at hit point</param>
        void OnHit(Vector3 hitPoint, Vector3 normal);
        
        /// <summary>
        /// Called when projectile is destroyed
        /// </summary>
        void OnDestroy();
        
        /// <summary>
        /// Update velocity-based effects (trails, particles)
        /// </summary>
        /// <param name="velocity">Current projectile velocity</param>
        void OnVelocityChanged(Vector3 velocity);
        
        /// <summary>
        /// Enable or disable trail effects
        /// </summary>
        /// <param name="enabled">Whether to enable trails</param>
        void SetTrailEnabled(bool enabled);
        
        /// <summary>
        /// Check if trail effects are enabled
        /// </summary>
        /// <returns>True if trails are enabled</returns>
        bool IsTrailEnabled();
        
        /// <summary>
        /// Set trail material
        /// </summary>
        /// <param name="material">Material for trail renderer</param>
        void SetTrailMaterial(Material material);
        
        /// <summary>
        /// Enable or disable particle effects
        /// </summary>
        /// <param name="enabled">Whether to enable particles</param>
        void SetParticlesEnabled(bool enabled);
        
        /// <summary>
        /// Check if particle effects are enabled
        /// </summary>
        /// <returns>True if particles are enabled</returns>
        bool IsParticlesEnabled();
        
        /// <summary>
        /// Set projectile color/material properties
        /// </summary>
        /// <param name="color">Color to apply</param>
        void SetColor(Color color);
        
        /// <summary>
        /// Get current projectile color
        /// </summary>
        /// <returns>Current color</returns>
        Color GetColor();
        
        /// <summary>
        /// Set projectile scale
        /// </summary>
        /// <param name="scale">Uniform scale factor</param>
        void SetScale(float scale);
        
        /// <summary>
        /// Get current projectile scale
        /// </summary>
        /// <returns>Current scale factor</returns>
        float GetScale();
        
        /// <summary>
        /// Clean up all effects for pooling
        /// </summary>
        void CleanupEffects();
        
        /// <summary>
        /// Reset effects for pooling
        /// </summary>
        void ResetForPool();
    }
}
