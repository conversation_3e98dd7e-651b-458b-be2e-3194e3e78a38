using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// Interface for projectile lifecycle components.
    /// Handles lifetime management, destruction conditions, and pooling integration.
    /// </summary>
    public interface IProjectileLifecycle
    {
        /// <summary>
        /// Initialize the lifecycle component with the parent projectile entity
        /// </summary>
        /// <param name="projectile">The projectile entity this component belongs to</param>
        void Initialize(ProjectileEntity projectile);
        
        /// <summary>
        /// Update lifecycle logic - called from Update
        /// </summary>
        /// <param name="deltaTime">Time since last update</param>
        void UpdateLifecycle(float deltaTime);
        
        /// <summary>
        /// Check if projectile should be destroyed
        /// </summary>
        /// <returns>True if projectile should be destroyed</returns>
        bool ShouldDestroy();
        
        /// <summary>
        /// Handle destruction logic and cleanup
        /// </summary>
        void OnDestroy();
        
        /// <summary>
        /// Set projectile lifetime
        /// </summary>
        /// <param name="lifetime">Lifetime in seconds</param>
        void SetLifetime(float lifetime);
        
        /// <summary>
        /// Get current remaining lifetime
        /// </summary>
        /// <returns>Remaining lifetime in seconds</returns>
        float GetRemainingLifetime();
        
        /// <summary>
        /// Get total lifetime
        /// </summary>
        /// <returns>Total lifetime in seconds</returns>
        float GetTotalLifetime();
        
        /// <summary>
        /// Get time alive since spawn
        /// </summary>
        /// <returns>Time alive in seconds</returns>
        float GetTimeAlive();
        
        /// <summary>
        /// Pause or resume lifetime countdown
        /// </summary>
        /// <param name="paused">Whether to pause the lifetime</param>
        void SetLifetimePaused(bool paused);
        
        /// <summary>
        /// Check if lifetime is currently paused
        /// </summary>
        /// <returns>True if lifetime is paused</returns>
        bool IsLifetimePaused();
        
        /// <summary>
        /// Extend current lifetime by specified amount
        /// </summary>
        /// <param name="extension">Additional time in seconds</param>
        void ExtendLifetime(float extension);
        
        /// <summary>
        /// Set maximum distance before destruction
        /// </summary>
        /// <param name="maxDistance">Maximum distance in world units</param>
        void SetMaxDistance(float maxDistance);
        
        /// <summary>
        /// Get maximum distance setting
        /// </summary>
        /// <returns>Maximum distance in world units</returns>
        float GetMaxDistance();
        
        /// <summary>
        /// Force immediate destruction
        /// </summary>
        /// <param name="hitTarget">Whether destruction was due to hitting target</param>
        void ForceDestroy(bool hitTarget = false);
        
        /// <summary>
        /// Reset lifecycle for pooling
        /// </summary>
        void ResetForPool();
    }
}
