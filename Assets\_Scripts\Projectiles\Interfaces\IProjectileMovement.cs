using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// Interface for projectile movement components.
    /// Handles all movement-related functionality including homing, linear movement, and job system integration.
    /// </summary>
    public interface IProjectileMovement
    {
        /// <summary>
        /// Initialize the movement component with the parent projectile entity
        /// </summary>
        /// <param name="projectile">The projectile entity this component belongs to</param>
        void Initialize(ProjectileEntity projectile);
        
        /// <summary>
        /// Update movement logic - called from FixedUpdate
        /// </summary>
        /// <param name="deltaTime">Time since last update</param>
        void UpdateMovement(float deltaTime);
        
        /// <summary>
        /// Get the current velocity of the projectile
        /// </summary>
        /// <returns>Current velocity vector</returns>
        Vector3 GetCurrentVelocity();
        
        /// <summary>
        /// Check if movement is complete (e.g., reached target, max distance)
        /// </summary>
        /// <returns>True if movement should stop</returns>
        bool IsMovementComplete();
        
        /// <summary>
        /// Enable or disable homing behavior
        /// </summary>
        /// <param name="enable">Whether to enable homing</param>
        void EnableHoming(bool enable);
        
        /// <summary>
        /// Set the target for homing projectiles
        /// </summary>
        /// <param name="target">Target transform to home towards</param>
        void SetTarget(Transform target);
        
        /// <summary>
        /// Get the current target
        /// </summary>
        /// <returns>Current target transform, null if no target</returns>
        Transform GetTarget();
        
        /// <summary>
        /// Set movement speed
        /// </summary>
        /// <param name="speed">New movement speed</param>
        void SetSpeed(float speed);
        
        /// <summary>
        /// Get current movement speed
        /// </summary>
        /// <returns>Current speed value</returns>
        float GetSpeed();
        
        /// <summary>
        /// Get distance traveled since spawn
        /// </summary>
        /// <returns>Total distance traveled</returns>
        float GetDistanceTraveled();
        
        /// <summary>
        /// Check if projectile is currently homing
        /// </summary>
        /// <returns>True if homing is enabled and active</returns>
        bool IsHoming();
    }
}
