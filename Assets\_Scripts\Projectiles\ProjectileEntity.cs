using UnityEngine;
using Chronos;
using BTR.EnemySystem.Entities;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Base class for the new component-based projectile system.
    /// This will eventually replace ProjectileStateBased as the main projectile entity.
    /// Implements IEntity and ICombatEntity for integration with the enemy system.
    /// </summary>
    public class ProjectileEntity : MonoBehaviour, IEntity, ICombatEntity, IProjectile
    {
        #region Component References
        [Header("Component References")]
        [SerializeField] private ProjectileMovement movementComponent;
        [SerializeField] private ProjectileCombat combatComponent;
        [SerializeField] private ProjectileLifecycle lifecycleComponent;
        [SerializeField] private ProjectileEffects effectsComponent;
        #endregion

        #region Core Properties (migrated from ProjectileStateBased)
        [Header("Core Properties")]
        public float BulletSpeed { get; set; } = 25f;
        public float DamageAmount { get; set; } = 10f;
        public float Lifetime { get; set; } = 5f;
        public bool IsPlayerShot { get; set; } = false;
        public bool Homing { get; set; } = false;
        public Transform CurrentTarget { get; set; }

        [Header("Performance Integration")]
        public int JobSystemIndex { get; set; } = -1;
        public bool IsPooled { get; set; } = false;
        #endregion

        #region Cached Components
        public Rigidbody Rigidbody { get; private set; }
        public Timeline Timeline { get; private set; }
        public Collider Collider { get; private set; }
        #endregion

        #region Component Access Properties
        /// <summary>
        /// Access to movement component functionality
        /// </summary>
        public IProjectileMovement Movement => movementComponent;

        /// <summary>
        /// Access to combat component functionality
        /// </summary>
        public IProjectileCombat Combat => combatComponent;

        /// <summary>
        /// Access to lifecycle component functionality
        /// </summary>
        public IProjectileLifecycle Lifecycle => lifecycleComponent;

        /// <summary>
        /// Access to effects component functionality
        /// </summary>
        public IProjectileEffects Effects => effectsComponent;
        #endregion

        #region IEntity Implementation
        public string EntityID { get; private set; }
        public GameObject GameObject => gameObject;
        public Transform Transform => transform;
        public bool IsInitialized { get; private set; }
        public bool IsActive { get; private set; }

        public event System.Action OnInitialized;
        public event System.Action OnActivated;
        public event System.Action OnDeactivated;
        #endregion

        #region ICombatEntity Implementation
        public float Health { get; private set; } = 1f;
        public float MaxHealth { get; private set; } = 1f;
        public bool IsVulnerable { get; private set; } = false; // Projectiles typically don't take damage
        public bool IsAlive => Health > 0f && gameObject.activeInHierarchy;

        public event System.Action<float> OnDamageReceived;
        public event System.Action<float, float> OnHealthChanged;
        public event System.Action OnDeath;
        public event System.Action<bool> OnVulnerabilityChanged;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            // Generate unique entity ID
            EntityID = System.Guid.NewGuid().ToString();

            // Cache components
            CacheComponents();

            // Auto-discover components if not assigned
            DiscoverComponents();
        }

        private void Start()
        {
            if (!IsInitialized)
            {
                Initialize();
            }
        }

        private void Update()
        {
            if (!IsActive) return;

            UpdateComponents();
        }

        private void FixedUpdate()
        {
            if (!IsActive) return;

            FixedUpdateComponents();
        }

        private void OnEnable()
        {
            if (IsInitialized && !IsActive)
            {
                Activate();
            }
        }

        private void OnDisable()
        {
            if (IsActive)
            {
                Deactivate();
            }
        }
        #endregion

        #region Initialization Methods
        private void CacheComponents()
        {
            Rigidbody = GetComponent<Rigidbody>();
            Timeline = GetComponent<Timeline>();
            Collider = GetComponent<Collider>();
        }

        private void DiscoverComponents()
        {
            // Auto-find components if not assigned in inspector
            if (!movementComponent) movementComponent = GetComponent<ProjectileMovement>();
            if (!combatComponent) combatComponent = GetComponent<ProjectileCombat>();
            if (!lifecycleComponent) lifecycleComponent = GetComponent<ProjectileLifecycle>();
            if (!effectsComponent) effectsComponent = GetComponent<ProjectileEffects>();
        }

        private void InitializeComponents()
        {
            // Initialize all components with this entity
            Movement?.Initialize(this);
            Combat?.Initialize(this);
            Lifecycle?.Initialize(this);
            Effects?.Initialize(this);
        }

        private void StartComponents()
        {
            // Start component functionality
            Effects?.OnSpawn();
        }

        private void UpdateComponents()
        {
            // Update components that need per-frame updates
            Lifecycle?.UpdateLifecycle(Time.deltaTime);
            Effects?.OnUpdate();
        }

        private void FixedUpdateComponents()
        {
            // Update components that need physics updates
            Movement?.UpdateMovement(Time.fixedDeltaTime);
        }
        #endregion

        #region IEntity Implementation Methods
        public void Initialize()
        {
            if (IsInitialized) return;

            try
            {
                InitializeComponents();
                IsInitialized = true;
                OnInitialized?.Invoke();

                Debug.Log($"[ProjectileEntity] Entity {EntityID} initialized successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileEntity] Failed to initialize entity {EntityID}: {e.Message}");
            }
        }

        public void Activate()
        {
            if (!IsInitialized)
            {
                Initialize();
            }

            if (IsActive) return;

            try
            {
                StartComponents();
                IsActive = true;
                OnActivated?.Invoke();

                Debug.Log($"[ProjectileEntity] Entity {EntityID} activated");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileEntity] Failed to activate entity {EntityID}: {e.Message}");
            }
        }

        public void Deactivate()
        {
            if (!IsActive) return;

            try
            {
                IsActive = false;
                OnDeactivated?.Invoke();

                Debug.Log($"[ProjectileEntity] Entity {EntityID} deactivated");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileEntity] Failed to deactivate entity {EntityID}: {e.Message}");
            }
        }

        public void Cleanup()
        {
            try
            {
                if (IsActive)
                {
                    Deactivate();
                }

                // Cleanup components
                Effects?.CleanupEffects();
                Lifecycle?.ResetForPool();
                Effects?.ResetForPool();

                // Reset properties
                IsInitialized = false;

                Debug.Log($"[ProjectileEntity] Entity {EntityID} cleaned up");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileEntity] Failed to cleanup entity {EntityID}: {e.Message}");
            }
        }
        #endregion

        #region Unity Lifecycle
        private void Update()
        {
            if (!IsActive) return;

            float deltaTime = Time.deltaTime;

            try
            {
                // Update components
                Movement?.UpdateMovement(deltaTime);
                Lifecycle?.UpdateLifetime(deltaTime);
                Effects?.UpdateEffects(deltaTime);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileEntity] Error during update for {EntityID}: {e.Message}");
            }
        }
        #endregion

        #region ICombatEntity Implementation Methods
        public void TakeDamage(float damage)
        {
            // Projectiles typically don't take damage, but implement for interface compliance
            if (!IsVulnerable) return;

            float oldHealth = Health;
            Health = Mathf.Max(0f, Health - damage);

            OnDamageReceived?.Invoke(damage);
            OnHealthChanged?.Invoke(oldHealth, Health);

            if (Health <= 0f && oldHealth > 0f)
            {
                OnDeath?.Invoke();
            }
        }

        public void Heal(float amount)
        {
            // Projectiles typically don't heal, but implement for interface compliance
            float oldHealth = Health;
            Health = Mathf.Min(MaxHealth, Health + amount);

            if (Health != oldHealth)
            {
                OnHealthChanged?.Invoke(oldHealth, Health);
            }
        }

        public void SetVulnerability(bool isVulnerable)
        {
            bool oldVulnerable = IsVulnerable;
            IsVulnerable = isVulnerable;

            if (oldVulnerable != isVulnerable)
            {
                OnVulnerabilityChanged?.Invoke(isVulnerable);
            }
        }

        public void SetMaxHealth(float maxHealth)
        {
            MaxHealth = maxHealth;
            // Clamp current health to new max
            if (Health > MaxHealth)
            {
                float oldHealth = Health;
                Health = MaxHealth;
                OnHealthChanged?.Invoke(oldHealth, Health);
            }
        }

        public void ResetHealth()
        {
            float oldHealth = Health;
            Health = MaxHealth;

            if (oldHealth != Health)
            {
                OnHealthChanged?.Invoke(oldHealth, Health);
            }
        }
        #endregion

        #region IProjectile Implementation
        public int InstanceID => gameObject.GetInstanceID();
        public int ProjectileIndex { get; set; } = -1;
        public bool HasHitTarget { get; set; } = false;

        public ProjectileSystemType GetProjectileSystemType()
        {
            return ProjectileSystemType.Component;
        }

        public void Death(bool hitTarget = false)
        {
            HasHitTarget = hitTarget;
            Lifecycle?.ForceDestroy(hitTarget);
            Cleanup();
        }

        public void ResetForPool()
        {
            HasHitTarget = false;
            ProjectileIndex = -1;
            CurrentTarget = null;

            // Reset component states
            Lifecycle?.ResetForPool();
            Effects?.ResetForPool();

            // Reset entity state
            if (IsActive)
            {
                Deactivate();
            }
        }

        public void PlaySound()
        {
            // TODO: Integrate with ProjectileAudioManager in Phase 2
            Debug.Log($"[ProjectileEntity] PlaySound called for {EntityID}");
        }

        /// <summary>
        /// Enable or disable homing behavior (delegates to Movement component)
        /// </summary>
        public void EnableHoming(bool enable)
        {
            Homing = enable;
            Movement?.EnableHoming(enable);
        }
        #endregion

        #region Public API Methods
        /// <summary>
        /// Setup projectile with initial parameters (similar to ProjectileStateBased.SetupProjectile)
        /// </summary>
        public void SetupProjectile(float damage, float speed, float lifetime, bool enableHoming, float scale, Transform target)
        {
            DamageAmount = damage;
            BulletSpeed = speed;
            Lifetime = lifetime;
            Homing = enableHoming;
            CurrentTarget = target;

            // Apply scale
            transform.localScale = Vector3.one * scale;

            // Configure components
            Combat?.SetDamage(damage);
            Movement?.SetSpeed(speed);
            Movement?.EnableHoming(enableHoming);
            Movement?.SetTarget(target);
            Lifecycle?.SetLifetime(lifetime);
            Effects?.SetScale(scale);

            Debug.Log($"[ProjectileEntity] Projectile setup - Damage: {damage}, Speed: {speed}, Lifetime: {lifetime}, Homing: {enableHoming}, Scale: {scale}, Target: {(target != null ? target.name : "None")}");
        }
        #endregion
    }
}
