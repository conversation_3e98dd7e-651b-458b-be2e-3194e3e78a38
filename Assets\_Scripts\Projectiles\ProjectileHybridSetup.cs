#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Editor utility to convert a ProjectileEntity prefab into a hybrid prefab 
    /// that works with both legacy and new systems.
    /// </summary>
    public class ProjectileHybridSetup : EditorWindow
    {
        private GameObject projectileEntityPrefab;
        private string hybridPrefabName = "ProjectileHybrid";
        private string outputPath = "Assets/_Scripts/Projectiles/Prefabs/";

        [MenuItem("BTR/Projectiles/Create Hybrid Prefab")]
        public static void ShowWindow()
        {
            GetWindow<ProjectileHybridSetup>("Projectile Hybrid Setup");
        }

        private void OnGUI()
        {
            GUILayout.Label("Projectile Hybrid Prefab Creator", EditorStyles.boldLabel);
            GUILayout.Space(10);

            EditorGUILayout.HelpBox(
                "This tool creates a hybrid prefab that works with both legacy (ProjectileStateBased) " +
                "and new (ProjectileEntity) systems. The hybrid prefab will have both components " +
                "and can be used seamlessly with existing code.",
                MessageType.Info);

            GUILayout.Space(10);

            // Input fields
            projectileEntityPrefab = (GameObject)EditorGUILayout.ObjectField(
                "ProjectileEntity Prefab", 
                projectileEntityPrefab, 
                typeof(GameObject), 
                false);

            hybridPrefabName = EditorGUILayout.TextField("Hybrid Prefab Name", hybridPrefabName);
            outputPath = EditorGUILayout.TextField("Output Path", outputPath);

            GUILayout.Space(10);

            // Validation
            bool canCreate = projectileEntityPrefab != null && 
                           projectileEntityPrefab.GetComponent<ProjectileEntity>() != null;

            if (!canCreate)
            {
                EditorGUILayout.HelpBox(
                    "Please assign a prefab that has a ProjectileEntity component.",
                    MessageType.Warning);
            }

            // Create button
            GUI.enabled = canCreate;
            if (GUILayout.Button("Create Hybrid Prefab", GUILayout.Height(30)))
            {
                CreateHybridPrefab();
            }
            GUI.enabled = true;

            GUILayout.Space(10);

            // Instructions
            EditorGUILayout.HelpBox(
                "After creating the hybrid prefab:\n" +
                "1. Assign it to both 'Legacy Projectile Prefab' and 'New Projectile Prefab' fields in ProjectilePool\n" +
                "2. Set 'Prefer New System' to true\n" +
                "3. The hybrid prefab will work with all existing code!",
                MessageType.Info);
        }

        private void CreateHybridPrefab()
        {
            try
            {
                // Create output directory if it doesn't exist
                if (!AssetDatabase.IsValidFolder(outputPath.TrimEnd('/')))
                {
                    string[] pathParts = outputPath.TrimEnd('/').Split('/');
                    string currentPath = pathParts[0];
                    
                    for (int i = 1; i < pathParts.Length; i++)
                    {
                        string newPath = currentPath + "/" + pathParts[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, pathParts[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Instantiate the original prefab
                GameObject hybridInstance = PrefabUtility.InstantiatePrefab(projectileEntityPrefab) as GameObject;
                hybridInstance.name = hybridPrefabName;

                // Get the ProjectileEntity component
                ProjectileEntity entity = hybridInstance.GetComponent<ProjectileEntity>();
                if (entity == null)
                {
                    Debug.LogError("Selected prefab doesn't have a ProjectileEntity component!");
                    DestroyImmediate(hybridInstance);
                    return;
                }

                // Add ProjectileStateBased component if it doesn't exist
                ProjectileStateBased legacy = hybridInstance.GetComponent<ProjectileStateBased>();
                if (legacy == null)
                {
                    legacy = hybridInstance.AddComponent<ProjectileStateBased>();
                }

                // Configure the legacy component to work with the entity
                ConfigureLegacyComponent(legacy, entity);

                // Create the hybrid prefab
                string prefabPath = outputPath + hybridPrefabName + ".prefab";
                GameObject hybridPrefab = PrefabUtility.SaveAsPrefabAsset(hybridInstance, prefabPath);

                // Clean up the instance
                DestroyImmediate(hybridInstance);

                // Select the created prefab
                Selection.activeObject = hybridPrefab;
                EditorGUIUtility.PingObject(hybridPrefab);

                Debug.Log($"✅ Hybrid prefab created successfully at: {prefabPath}");
                
                EditorUtility.DisplayDialog(
                    "Success!", 
                    $"Hybrid prefab '{hybridPrefabName}' created successfully!\n\n" +
                    "Next steps:\n" +
                    "1. Assign it to ProjectilePool's Legacy and New prefab fields\n" +
                    "2. Set 'Prefer New System' to true\n" +
                    "3. Test your projectile system!",
                    "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to create hybrid prefab: {e.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to create hybrid prefab:\n{e.Message}", "OK");
            }
        }

        private void ConfigureLegacyComponent(ProjectileStateBased legacy, ProjectileEntity entity)
        {
            // Copy essential properties from entity to legacy component
            legacy.damageAmount = entity.DamageAmount;
            legacy.bulletSpeed = entity.BulletSpeed;
            legacy.lifetime = entity.Lifetime;
            legacy.homing = entity.Homing;

            // Set up component references
            legacy.rb = entity.gameObject.GetComponent<Rigidbody>();
            if (legacy.rb == null)
            {
                legacy.rb = entity.gameObject.AddComponent<Rigidbody>();
            }

            // Find renderer (check for "Projectile Model" child first, then fallback)
            Transform projectileModel = entity.transform.Find("Projectile Model");
            if (projectileModel != null)
            {
                legacy.modelRenderer = projectileModel.GetComponent<Renderer>();
            }
            else
            {
                legacy.modelRenderer = entity.GetComponent<Renderer>();
            }

            // Add other required components if missing
            if (entity.GetComponent<Collider>() == null)
            {
                var collider = entity.gameObject.AddComponent<SphereCollider>();
                collider.isTrigger = true;
                collider.radius = 0.5f;
            }

            Debug.Log($"✅ Configured legacy component for {entity.name}");
        }
    }
}
#endif
