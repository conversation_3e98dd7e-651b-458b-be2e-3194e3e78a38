using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Unity.Cinemachine;
using Michsky.UI.Reach;
using PrimeTween;
using UnityEngine;
using UnityEngine.Events;
using FluffyUnderware.Curvy;
using FluffyUnderware.Curvy.Controllers;
using Chronos;
using UnityEngine.SceneManagement;
using UnityEditor;
using SonicBloom.Koreo;
using BTR;
using BTR.Projectiles;

namespace BTR
{
    [DefaultExecutionOrder(-250)]

    public class ProjectileManager : MonoBehaviour
    {
        public static ProjectileManager Instance { get; private set; }

        [SerializeField]
        private int staticShootingRequestsPerFrame = 10;

        private const int MAX_PROJECTILES = 1000;
        private ProjectileStateBased[] projectileArray;
        private bool[] activeSlots;
        private ProjectileStateBased[] playerProjectileArray;
        private ProjectileStateBased[] enemyProjectileArray;
        private int playerProjectileCount;
        private int enemyProjectileCount;
        private ProjectileGrid projectileGrid;

        [SerializeField] private LayerMask playerLayerMask;
        [SerializeField] private LayerMask enemyLayerMask;
        [SerializeField] public float projectileAccuracy = 1f;

        private bool isTransitioning;
        private ProjectileJobSystem projectileJobSystem;
        private ProjectilePool projectilePool;
        private ProjectileSpawner projectileSpawner;
        private Dictionary<GameObject, Vector3> lastPositions = new Dictionary<GameObject, Vector3>();
        private Dictionary<int, Transform> enemyTransforms = new Dictionary<int, Transform>();
        private float lastEnemyUpdateTime = 0f;
        private const float ENEMY_UPDATE_INTERVAL = 0.5f;
        private HashSet<int> homingProjectileIds = new HashSet<int>();

        // Debug flag to control homing projectile logs
        [Header("Debug Settings")]
        [SerializeField] private bool enableHomingDebugLogs = false;

        [Header("Coordinated Attack Settings")]
        [SerializeField, EventID, Tooltip("The Koreographer event ID that triggers coordinated attacks. Projectiles will attack on each beat of this event.")]
        private string coordinatedAttackEventID = "Coordinated Attack";

        [Header("Group Attack Settings")]
        [SerializeField, Tooltip("Enable or disable group attack behavior")]
        private bool enableGroupAttacks = true;

        [System.Serializable]
        private class GroupAttackSettings
        {
            [SerializeField, Tooltip("The range at which projectiles will start grouping together for a coordinated attack.")]
            public float groupAttackRange = 10f;

            [SerializeField, Tooltip("The minimum number of projectiles needed to form a group attack.")]
            public int minProjectilesForGroup = 3;

            [SerializeField, Tooltip("The color that grouped projectiles will glow while preparing to attack.")]
            public Color groupAttackColor = new Color(1f, 0.5f, 0f, 1f);

            [SerializeField, Tooltip("The distance from the player at which grouped projectiles will position themselves.")]
            public float groupAttackDistance = 5f;

            [SerializeField, Tooltip("How much faster grouped projectiles move when taking their positions.")]
            public float groupAttackSpeed = 2f;

            [SerializeField, Tooltip("How long projectiles will wait in formation before attacking.")]
            public float groupAttackDelay = 1f;

            [SerializeField, Tooltip("Toggle to show/hide debug visualization of group attack ranges.")]
            public bool showGroupDebugVisualization = true;
        }

        [SerializeField]
        private GroupAttackSettings groupAttackConfig;

        private void OnValidate()
        {
            if (!enableGroupAttacks)
            {
                groupAttackConfig = null;
            }
            else if (groupAttackConfig == null)
            {
                groupAttackConfig = new GroupAttackSettings();
            }
        }

        private Queue<ProjectileStateBased> groupAttackQueue = new Queue<ProjectileStateBased>();
        private float groupAttackStartTime;

        public bool IsInitialized { get; private set; }

        private const int BATCH_SIZE = 64;
        private const float POSITION_UPDATE_THRESHOLD = 0.1f; // Only update grid position if moved more than this
        private float updateInterval = 0.033f; // Update at ~30Hz for far projectiles
        private float nearUpdateInterval = 0.016f; // Update at ~60Hz for near projectiles
        private float distanceThreshold = 20f; // Distance to consider a projectile "near"
        private float lastUpdateTime;

        // Cache arrays and lists to avoid allocations
        private List<ProjectileStateBased> projectilesToProcess = new List<ProjectileStateBased>();
        private List<ProjectileStateBased> nearbyProjectilesList; // Remove initialization here

        private int currentProjectileIndex = 0;

        // Cache the array to avoid allocations
        private RaycastHit[] raycastHits = new RaycastHit[10]; // Adjust the size as needed

        private class PlayerShotTrail
        {
            public LineRenderer lineRenderer;
            public float startTime;
            public float duration;
            public Vector3 startPos;
            public Vector3 endPos;
            public Color startColor;
            public Color endColor;
        }

        [Header("Player Shot Trails")]
        [SerializeField] private float trailDuration = 1f;
        [SerializeField] private float trailStartWidth = 0.2f;
        [SerializeField] private float trailEndWidth = 0.05f;
        [SerializeField] private Color trailStartColor = Color.white;
        [SerializeField] private Color trailEndColor = new Color(1f, 1f, 1f, 0f);
        [SerializeField] private Material trailMaterial;
        private List<PlayerShotTrail> activeTrails = new List<PlayerShotTrail>();
        private Queue<LineRenderer> trailPool = new Queue<LineRenderer>();
        private const int INITIAL_TRAIL_POOL_SIZE = 20;

        private GlobalClock globalClock;
        private bool isClockInitialized = false;
        private SplineController playerSplineController;  // Add spline controller reference

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            projectileJobSystem = gameObject.AddComponent<ProjectileJobSystem>();
            if (projectileJobSystem == null)
            {
                Debug.LogError("[ProjectileManager] Failed to add ProjectileJobSystem component");
            }

            currentProjectileIndex = 0;
        }

        private void Start()
        {
            if (!IsInitialized)
            {
                Initialize();
            }
            InitializeTrailPool();
            StartCoroutine(InitializeChronosClock());

            // Register for Koreographer events
            if (Koreographer.Instance != null)
            {
                Koreographer.Instance.RegisterForEvents(coordinatedAttackEventID, OnCoordinatedAttackBeat);
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogProjectileDeath(
                        "System",
                        ProjectileDeathReason.Unknown,
                        0,
                        0,
                        0,
                        Vector3.zero
                    );
                }
            }
        }

        private IEnumerator InitializeChronosClock()
        {
            // Wait a frame to ensure Chronos is initialized
            yield return null;

            int retryCount = 0;
            while (retryCount < 3)
            {
                try
                {
                    if (Timekeeper.instance != null)
                    {
                        globalClock = Timekeeper.instance.Clock("Test");
                        if (globalClock != null)
                        {
                            if (ProjectileLogger.Instance != null)
                            {
                                ProjectileLogger.Instance.LogProjectileDeath(
                                    "System",
                                    ProjectileDeathReason.Unknown,
                                    0,
                                    0,
                                    0,
                                    Vector3.zero
                                );
                            }
                            isClockInitialized = true;
                            yield break;
                        }
                    }
                }
                catch (ChronosException e)
                {
                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogProjectileDeath(
                            "System",
                            ProjectileDeathReason.Unknown,
                            0,
                            0,
                            0,
                            Vector3.zero
                        );
                    }
                }

                retryCount++;
                yield return new WaitForSeconds(0.1f);
            }

            if (ProjectileLogger.Instance != null)
            {
                ProjectileLogger.Instance.LogProjectileDeath(
                    "System",
                    ProjectileDeathReason.Unknown,
                    0,
                    0,
                    0,
                    Vector3.zero
                );
            }
            globalClock = null;
            isClockInitialized = true; // Still mark as initialized so we can proceed with default time scale
        }

        private void Initialize()
        {
            projectileGrid = gameObject.AddComponent<ProjectileGrid>();
            projectilePool = ProjectilePool.Instance;
            projectileSpawner = ProjectileSpawner.Instance;

            if (projectilePool == null || projectileSpawner == null)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogProjectileDeath(
                        "System",
                        ProjectileDeathReason.Unknown,
                        0,
                        0,
                        0,
                        Vector3.zero
                    );
                }
                enabled = false;
                return;
            }

            projectileArray = new ProjectileStateBased[MAX_PROJECTILES];
            activeSlots = new bool[MAX_PROJECTILES];
            playerProjectileArray = new ProjectileStateBased[MAX_PROJECTILES];
            enemyProjectileArray = new ProjectileStateBased[MAX_PROJECTILES];
            playerProjectileCount = 0;
            enemyProjectileCount = 0;
            nearbyProjectilesList = projectileGrid.GetPooledResultsList();

            IsInitialized = true;
        }

        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            StartCoroutine(InitializeAfterSceneLoad());
        }

        private IEnumerator InitializeAfterSceneLoad()
        {
            yield return new WaitForSeconds(0.1f);
            ReRegisterEnemiesAndProjectiles();
        }

        private void Update()
        {
            if (!IsInitialized || !isClockInitialized || isTransitioning) return;
            if (projectileJobSystem == null) return;

            try
            {
                UpdateTrails();
                UpdateGroupAttack();

                // Schedule movement update with scaled time
                float timeScale = globalClock != null ? globalClock.localTimeScale : 1f;
                float scaledDeltaTime = Time.deltaTime * timeScale;
                projectileJobSystem.ScheduleProjectileMovement(scaledDeltaTime);

                // Update positions after job completion
                projectileJobSystem.CompleteProjectileUpdate();
                UpdateProjectilesByDistance();

                ProcessProjectileRequests();
                ProcessCollisionResults();
                UpdateProjectileTargets();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileManager] Error in Update: {e.Message}");
            }
        }

        private void UpdateProjectilesByDistance()
        {
            if (!IsInitialized || projectileArray == null) return;

            var playerPos = Camera.main?.transform.position ?? Vector3.zero;
            float currentTimeScale = globalClock != null ? globalClock.localTimeScale : 1f;
            float scaledUpdateInterval = updateInterval;
            float scaledNearUpdateInterval = nearUpdateInterval;

            // Process all active projectiles
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (!activeSlots[i]) continue;

                var projectile = projectileArray[i];
                if (projectile == null || !projectile.gameObject.activeInHierarchy)
                {
                    activeSlots[i] = false;
                    continue;
                }

                Vector3 oldPosition = projectile.transform.position;

                // Get updated position from job system
                if (projectile._movement != null)
                {
                    Vector3 newPosition = projectileJobSystem.GetProjectilePosition(i);
                    Quaternion newRotation = projectileJobSystem.GetProjectileRotation(i);
                    Vector3 newVelocity = projectileJobSystem.GetProjectileVelocity(i);

                    projectile._movement.ApplyJobResults(newPosition, newRotation, newVelocity);

                    // Only update grid if moved significantly
                    if (Vector3.Distance(oldPosition, newPosition) > POSITION_UPDATE_THRESHOLD)
                    {
                        projectileGrid.UpdateProjectileGridPosition(projectile, oldPosition, newPosition);
                    }
                }
                else
                {
                    // Fallback for projectiles without movement component
                    projectile.UpdatePosition();

                    // Only update grid if moved significantly
                    Vector3 newPosition = projectile.transform.position;
                    if (Vector3.Distance(oldPosition, newPosition) > POSITION_UPDATE_THRESHOLD)
                    {
                        projectileGrid.UpdateProjectileGridPosition(projectile, oldPosition, newPosition);
                    }
                }
            }

            UpdateProjectileLists();
        }

        private void UpdateProjectileLists()
        {
            playerProjectileCount = 0;
            enemyProjectileCount = 0;

            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (projectileArray[i] != null && projectileArray[i].gameObject.activeInHierarchy)
                {
                    if (projectileArray[i].isPlayerShot)
                    {
                        playerProjectileArray[playerProjectileCount] = projectileArray[i];
                        playerProjectileCount++;
                    }
                    else
                    {
                        enemyProjectileArray[enemyProjectileCount] = projectileArray[i];
                        enemyProjectileCount++;
                    }
                }
            }
        }

        private void OnSceneTransitionStart()
        {
            isTransitioning = true;
            ClearAllProjectiles();
        }

        private void OnSceneTransitionEnd()
        {
            isTransitioning = false;
        }

        private void OnSceneUnloaded(Scene scene)
        {
            ClearAllProjectiles();
        }

        public void RegisterProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null || isTransitioning) return;

            int index = projectile.projectileIndex;
            if (index < 0 || index >= MAX_PROJECTILES)
            {
                index = GetNextProjectileIndex();
                if (index == -1) return; // No slots available
                projectile.projectileIndex = index;
            }

            lock (activeSlots) // Add thread safety
            {
                projectileArray[index] = projectile;
                activeSlots[index] = true;

                // Update job system data
                projectileJobSystem.UpdateProjectileMovementData(
                    index,
                    projectile.transform.position,
                    projectile.transform.rotation,
                    projectile.rb.linearVelocity,
                    projectile.currentTarget != null ? projectile.currentTarget.position : projectile.transform.position + projectile.transform.forward * 100f,
                    projectile.homing && projectile.currentTarget != null,
                    projectile._rotateSpeed,
                    projectile.bulletSpeed,
                    1f,
                    projectile.lifetime
                );

                projectile.SetAccuracy(projectileAccuracy);

                // Update player/enemy arrays
                if (projectile.isPlayerShot)
                {
                    playerProjectileArray[playerProjectileCount] = projectile;
                    playerProjectileCount++;
                }
                else
                {
                    enemyProjectileArray[enemyProjectileCount] = projectile;
                    enemyProjectileCount++;
                }
            }
        }

        /// <summary>
        /// Register a projectile using the unified IProjectile interface
        /// </summary>
        public void RegisterProjectileInterface(IProjectile projectile)
        {
            if (projectile == null || isTransitioning) return;

            if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // For legacy projectiles, use the existing registration method
                var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                var legacyProjectile = adapter?.GetProjectileStateBased();
                if (legacyProjectile != null)
                {
                    RegisterProjectile(legacyProjectile);
                }
            }
            else
            {
                // For new system projectiles, implement new registration logic
                RegisterNewSystemProjectile(projectile);
            }
        }

        /// <summary>
        /// Register a new system projectile (ProjectileEntity)
        /// </summary>
        private void RegisterNewSystemProjectile(IProjectile projectile)
        {
            // For now, we'll implement basic registration
            // This would be expanded to work with the job system and other features

            Debug.Log($"[ProjectileManager] Registered new system projectile: {projectile.InstanceID}");

            // Add to spatial grid for collision detection
            if (projectileGrid != null && projectile.GameObject != null)
            {
                // Note: This assumes projectileGrid can work with GameObjects
                // You might need to adapt this based on your grid implementation
                var transform = projectile.Transform;
                if (transform != null)
                {
                    // For now, just log the registration
                    Debug.Log($"[ProjectileManager] Added new projectile to spatial tracking at {transform.position}");
                }
            }
        }

        public void UnregisterProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            int index = projectile.projectileIndex;
            if (!IsValidProjectileIndex(index)) return;

            lock (activeSlots)
            {
                // Log enemy projectile deaths
                if (!projectile.isPlayerShot && ProjectileLogger.Instance != null)
                {
                    ProjectileDeathReason reason = projectile.projHitPlayer ?
                        ProjectileDeathReason.HitPlayer : ProjectileDeathReason.Timeout;

                    var player = GameObject.FindWithTag("Player")?.GetComponent<PlayerHealth>();
                    float currentHealth = player != null ? player.CurrentHealth : 0;

                    ProjectileLogger.Instance.LogProjectileDeath(
                        projectile.GetInstanceID().ToString(),
                        reason,
                        projectile.CalculateDamage(),
                        currentHealth,
                        currentHealth,
                        projectile.transform.position
                    );
                }

                // Clear from main array and mark slot as available
                projectileArray[index] = null;
                activeSlots[index] = false;

                // Deactivate in job system
                projectileJobSystem?.DeactivateProjectile(index);

                // Remove from player/enemy arrays by shifting remaining elements
                if (projectile.isPlayerShot)
                {
                    RemoveFromPlayerArray(projectile);
                }
                else
                {
                    RemoveFromEnemyArray(projectile);
                }

                // Remove from grid
                projectileGrid?.RemoveProjectile(projectile);

                // Unregister homing projectile
                homingProjectileIds.Remove(projectile.GetInstanceID());
            }
        }

        private void RemoveFromPlayerArray(ProjectileStateBased projectile)
        {
            for (int i = 0; i < playerProjectileCount; i++)
            {
                if (playerProjectileArray[i] == projectile)
                {
                    // Shift remaining elements
                    Array.Copy(playerProjectileArray, i + 1, playerProjectileArray, i, playerProjectileCount - i - 1);
                    playerProjectileArray[playerProjectileCount - 1] = null;
                    playerProjectileCount--;
                    break;
                }
            }
        }

        private void RemoveFromEnemyArray(ProjectileStateBased projectile)
        {
            for (int i = 0; i < enemyProjectileCount; i++)
            {
                if (enemyProjectileArray[i] == projectile)
                {
                    // Shift remaining elements
                    Array.Copy(enemyProjectileArray, i + 1, enemyProjectileArray, i, enemyProjectileCount - i - 1);
                    enemyProjectileArray[enemyProjectileCount - 1] = null;
                    enemyProjectileCount--;
                    break;
                }
            }
        }

        public int GetNextProjectileIndex()
        {
            lock (activeSlots) // Add thread safety since this might be called from multiple threads
            {
                // First try to reuse the current index if possible
                if (currentProjectileIndex < MAX_PROJECTILES && !activeSlots[currentProjectileIndex])
                {
                    int index = currentProjectileIndex;
                    currentProjectileIndex = (currentProjectileIndex + 1) % MAX_PROJECTILES;
                    return index;
                }

                // If current index is not available, search for the next available slot
                for (int i = 0; i < MAX_PROJECTILES; i++)
                {
                    if (!activeSlots[i])
                    {
                        currentProjectileIndex = (i + 1) % MAX_PROJECTILES;
                        return i;
                    }
                }

                Debug.LogWarning("[ProjectileManager] No free projectile slots available!");
                return -1;
            }
        }

        // Add a method to check if an index is valid and active
        public bool IsValidProjectileIndex(int index)
        {
            if (index < 0 || index >= MAX_PROJECTILES) return false;
            return activeSlots[index];
        }

        // Add a method to validate and get a projectile by index
        public ProjectileStateBased GetProjectileByIndex(int index)
        {
            if (!IsValidProjectileIndex(index)) return null;
            return projectileArray[index];
        }

        private void OnEnable()
        {
            SceneManager.sceneLoaded += OnSceneLoaded;
        }

        private void OnDisable()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }

        private void ProcessProjectileRequests()
        {
            if (isTransitioning) return;

            // Scale requests based on current time scale, default to 1 if clock is not available
            float timeScale = globalClock != null ? globalClock.localTimeScale : 1f;
            int requestsToProcess = Mathf.CeilToInt(staticShootingRequestsPerFrame * timeScale);

            for (int i = 0; i < requestsToProcess && ProjectilePool.Instance.HasPendingRequests(); i++)
            {
                if (ProjectilePool.Instance.TryDequeueProjectileRequest(out ProjectileSpawnRequest request))
                {
                    ProjectileStateBased projectile = projectilePool.GetProjectile();
                    if (projectile != null)
                    {
                        projectileSpawner.ProcessShootProjectile(request, projectile);
                    }
                }
            }
        }

        private void ProcessCollisionResults()
        {
            if (projectileArray == null) return;

            // Clear and reuse the list without LINQ
            projectilesToProcess.Clear();
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (!activeSlots[i]) continue;
                var projectile = projectileArray[i];
                if (projectile != null && projectile.gameObject.activeInHierarchy)
                {
                    projectilesToProcess.Add(projectile);
                }
            }

            foreach (var projectile in projectilesToProcess)
            {
                if (projectile == null || !projectile.gameObject.activeInHierarchy) continue;

                bool useRaycast = ShouldUseRaycastForProjectile(projectile);
                if (useRaycast)
                {
                    // Use RaycastNonAlloc to avoid allocations
                    Vector3 direction = projectile.transform.forward;
                    float rayDistance = projectile.bulletSpeed * Time.deltaTime;
                    int layerMask = GetLayerMaskForProjectile(projectile);

                    int hitCount = Physics.RaycastNonAlloc(
                        projectile.transform.position,
                        direction,
                        raycastHits,
                        rayDistance,
                        layerMask,
                        QueryTriggerInteraction.Collide
                    );

                    for (int i = 0; i < hitCount; i++)
                    {
                        var hit = raycastHits[i];
                        if (hit.collider != null)
                        {
                            projectile.OnTriggerEnter(hit.collider);
                        }
                    }
                }
                else
                {
                    // Reuse the list instead of creating a new one each time
                    nearbyProjectilesList.Clear();
                    projectileGrid.GetNearbyProjectiles(
                        projectile.transform.position,
                        3f,
                        projectile.isPlayerShot,
                        nearbyProjectilesList
                    );

                    foreach (var nearbyProjectile in nearbyProjectilesList)
                    {
                        if (nearbyProjectile == null || !nearbyProjectile.gameObject.activeInHierarchy) continue;

                        float sqrDistance = (projectile.transform.position - nearbyProjectile.transform.position).sqrMagnitude;
                        if (sqrDistance <= 9f) // 3f * 3f = 9f
                        {
                            projectile.OnTriggerEnter(nearbyProjectile.GetComponent<Collider>());
                        }
                    }
                }
            }
        }

        private bool ShouldUseRaycastForProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null || !projectile.gameObject.activeInHierarchy) return false;

            // Always use raycast for high-speed projectiles
            float speedThreshold = 50f; // Adjust based on your game's scale
            if (projectile.bulletSpeed > speedThreshold) return true;

            // Use raycast if projectile moved significantly since last frame
            Vector3 currentPos = projectile.transform.position;
            if (!lastPositions.TryGetValue(projectile.gameObject, out Vector3 lastPos))
            {
                lastPositions[projectile.gameObject] = currentPos;
                return false;
            }

            float distanceMoved = Vector3.Distance(currentPos, lastPos);
            lastPositions[projectile.gameObject] = currentPos;

            // Use raycast if moved more than half a grid cell
            return distanceMoved > (ProjectileGrid.GRID_CELL_SIZE * 0.5f);
        }

        public void CompleteRunningJobs()
        {
            if (projectileJobSystem != null)
            {
                projectileJobSystem.CompleteProjectileUpdate();
            }
        }

        public void UpdateProjectileTargets()
        {
            if (projectileArray == null) return;

            // Log the contents of the homing set at the start of the update
            if (homingProjectileIds.Count > 0 && enableHomingDebugLogs)
            {
                string idList = string.Join(", ", homingProjectileIds);
                Debug.Log($"[PM_UpdateTargets_Start] Current homingProjectileIds: [{idList}]");
            }

            var playerTransform = GameObject.FindWithTag("Player")?.transform;
            if (playerTransform == null) return;

            // Create a temporary list to avoid modification issues
            var homingIdsSnapshot = new List<int>(homingProjectileIds); // Use a snapshot for iteration
            foreach (var projectileId in homingIdsSnapshot) // Iterate over snapshot
            {
                // Find the projectile that has this instanceID
                ProjectileStateBased projectileToUpdate = null;
                int jobIndex = -1;
                for (int i = 0; i < MAX_PROJECTILES; ++i)
                {
                    if (activeSlots[i] && projectileArray[i] != null && projectileArray[i].GetInstanceID() == projectileId)
                    {
                        projectileToUpdate = projectileArray[i];
                        jobIndex = i;
                        break;
                    }
                }

                if (projectileToUpdate != null && projectileToUpdate.gameObject.activeInHierarchy && !projectileToUpdate.isPlayerShot)
                {
                    // Apply accuracy to target position
                    Vector3 targetPos = playerTransform.position;
                    if (projectileAccuracy < 1f)
                    {
                        float inaccuracyRadius = (1f - projectileAccuracy) * 5f; // 5 units max spread at 0 accuracy
                        Vector2 randomOffset = UnityEngine.Random.insideUnitCircle * inaccuracyRadius;
                        targetPos += new Vector3(randomOffset.x, 0, randomOffset.y);
                    }

                    // Create or update virtual target
                    if (projectileToUpdate.virtualTargetTransform == null)
                    {
                        var virtualTarget = new GameObject($"VirtualTarget_{projectileId}").transform;
                        projectileToUpdate.virtualTargetTransform = virtualTarget;
                    }
                    projectileToUpdate.virtualTargetTransform.position = targetPos;
                    projectileToUpdate.SetHomingTarget(projectileToUpdate.virtualTargetTransform);
                }
                else
                {
                    // Clean up invalid entries from the actual set if the projectile is no longer valid or is a player shot
                    if (projectileToUpdate == null || !projectileToUpdate.gameObject.activeInHierarchy || projectileToUpdate.isPlayerShot)
                    {
                        // This check is a bit redundant if we iterate a snapshot, but good for direct modification logic
                        homingProjectileIds.Remove(projectileId);
                    }
                }
            }
        }

        public void NotifyEnemyHit(GameObject enemy, ProjectileStateBased projectile)
        {
            if (projectile.GetCurrentState() is PlayerShotState)
            {
                PlayerLocking.Instance.RemoveLockedEnemy(enemy.transform);
            }
        }

        public Transform FindNearestEnemy(Vector3 position)
        {
            if (Time.time - lastEnemyUpdateTime > ENEMY_UPDATE_INTERVAL)
            {
                UpdateEnemyTransforms();
            }

            Transform nearestEnemy = null;
            float nearestDistanceSqr = float.MaxValue;
            Vector3 pos = position; // Cache to avoid struct copies

            foreach (var kvp in enemyTransforms)
            {
                Transform enemy = kvp.Value;
                if (enemy != null)
                {
                    float distanceSqr = (enemy.position - pos).sqrMagnitude;
                    if (distanceSqr < nearestDistanceSqr)
                    {
                        nearestDistanceSqr = distanceSqr;
                        nearestEnemy = enemy;
                    }
                }
            }

            return nearestEnemy;
        }

        private void UpdateEnemyTransforms()
        {
            enemyTransforms.Clear();
            var enemies = GameObject.FindGameObjectsWithTag("Enemy");
            foreach (var enemy in enemies)
            {
                enemyTransforms[enemy.GetInstanceID()] = enemy.transform;
            }
            lastEnemyUpdateTime = Time.time;
        }

        public void OnWaveStart()
        {
            lastEnemyUpdateTime = 0f;
            lastPositions.Clear();
            enemyTransforms.Clear();

            // Find and cache the SplineController when wave starts
            GameObject playerPlane = GameObject.FindGameObjectWithTag("PlayerPlane");
            if (playerPlane != null)
            {
                playerSplineController = playerPlane.GetComponent<SplineController>();
                if (playerSplineController != null)
                {
                    Debug.Log($"<color=green>[ProjectileManager] Found SplineController on PlayerPlane. Spline Ready: {playerSplineController.IsReady}, Spline: {playerSplineController.Spline != null}</color>");
                }
                else
                {
                    Debug.LogError("[ProjectileManager] PlayerPlane found but missing SplineController component");
                }
            }
            else
            {
                Debug.LogError("[ProjectileManager] Could not find GameObject with tag 'PlayerPlane'");
            }
        }

        public void OnWaveEnd()
        {
            ClearAllProjectiles();
            lastEnemyUpdateTime = 0f;
            lastPositions.Clear();
            enemyTransforms.Clear();
            playerSplineController = null;  // Clear spline controller reference
        }

        public void RegisterHomingProjectile(int projectileId)
        {
            homingProjectileIds.Add(projectileId);
        }

        public void UnregisterHomingProjectile(int projectileId)
        {
            // Log before removal
            if (homingProjectileIds.Contains(projectileId) && enableHomingDebugLogs) // Check if it exists before logging removal attempt
            {
                Debug.Log($"[PM_UnregisterHoming] Attempting to unregister homing for PID: {projectileId}");
            }
            homingProjectileIds.Remove(projectileId);
        }

        public IReadOnlyCollection<int> GetActiveHomingProjectileIds()
        {
            return homingProjectileIds;
        }

        public ProjectileJobSystem GetProjectileJobSystem()
        {
            return projectileJobSystem;
        }

        public ProjectileStateBased SpawnProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming = false,
            Material material = null,
            Transform target = null,
            bool isPlayerProjectile = false)
        {
            if (!ProjectileSpawner.Instance)
            {
                Debug.LogError("[ProjectileManager] ProjectileSpawner.Instance is null");
                return null;
            }

            if (isPlayerProjectile && target != null)
            {
                // Create visual trail for player shots
                CreatePlayerShotTrail(position, target.position);
            }

            if (isPlayerProjectile)
            {
                return ProjectileSpawner.Instance.ShootPlayerProjectile(damage, speed, scale);
            }

            var projectileInterface = ProjectileSpawner.Instance.ShootProjectileFromEnemy(
                position,
                rotation,
                speed,
                lifetime,
                scale,
                damage,
                enableHoming,
                material,
                "Test",
                projectileAccuracy,
                target
            );

            if (projectileInterface != null && enableHoming)
            {
                // Log right before registration
                if (enableHomingDebugLogs)
                {
                    Debug.Log($"[PM_Spawn] Intending to register homing for PID: {projectileInterface.InstanceID} because enableHoming parameter was true.");
                }
                RegisterHomingProjectile(projectileInterface.InstanceID);
            }

            // Handle job system integration based on projectile type
            if (projectileInterface != null && projectileJobSystem != null)
            {
                if (projectileInterface.GetProjectileSystemType() == ProjectileSystemType.Legacy)
                {
                    // For legacy projectiles, use existing job system integration
                    var adapter = projectileInterface as ProjectileStateBased_IProjectileAdapter;
                    var legacyProjectile = adapter?.GetProjectileStateBased();
                    if (legacyProjectile != null)
                    {
                        projectileJobSystem.UpdateProjectileMovementData(
                            legacyProjectile.projectileIndex,
                            legacyProjectile.transform.position,
                            legacyProjectile.transform.rotation,
                            legacyProjectile.rb.linearVelocity,
                            legacyProjectile.currentTarget != null ? legacyProjectile.currentTarget.position : legacyProjectile.transform.position + legacyProjectile.transform.forward * 100f,
                            legacyProjectile.homing && legacyProjectile.currentTarget != null,
                            legacyProjectile._rotateSpeed,
                            legacyProjectile.bulletSpeed,
                            1f, // Initial timeScale
                            legacyProjectile.lifetime
                        );
                    }
                }
                else
                {
                    // For new system projectiles, implement new job system integration
                    Debug.Log($"[ProjectileManager] New system projectile job integration - ID: {projectileInterface.InstanceID}");
                }
            }

            // Convert back to ProjectileStateBased for backward compatibility
            if (projectileInterface != null && projectileInterface.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                var adapter = projectileInterface as ProjectileStateBased_IProjectileAdapter;
                return adapter?.GetProjectileStateBased();
            }

            // For new system projectiles, we can't return a ProjectileStateBased
            // This indicates the calling code needs to be updated to use the unified interface
            if (projectileInterface != null)
            {
                Debug.LogWarning("[ProjectileManager] SpawnProjectile called but new system projectile created. " +
                               "Consider updating calling code to use SpawnProjectileInterface() method.");
            }

            return null;
        }

        // Keep the old method signature for backward compatibility
        public ProjectileStateBased SpawnProjectile(ProjectilePoolType poolType, Vector3 position, Quaternion rotation, Transform target = null)
        {
            // Use default values for backward compatibility
            return SpawnProjectile(poolType, position, rotation, 5f, 1f, 10f, target);
        }

        public ProjectileStateBased SpawnProjectile(ProjectilePoolType poolType, Vector3 position, Quaternion rotation, float lifetime, float scale, float damage, Transform target = null)
        {
            try
            {
                ProjectileSpawnRequest request = new ProjectileSpawnRequest
                {
                    Position = position,
                    Rotation = rotation,
                    Speed = 70f, // Use consistent speed for static enemies
                    Lifetime = lifetime,
                    Scale = scale,
                    Damage = damage,
                    EnableHoming = target != null,
                    Target = target,
                    IsStatic = poolType == ProjectilePoolType.Static,
                    MaterialId = (int)poolType
                };

                var projectile = projectilePool.GetProjectile(request);
                if (projectile != null)
                {
                    // Assign projectile index for job system
                    projectile.projectileIndex = GetNextProjectileIndex();

                    // Set critical values
                    projectile.bulletSpeed = request.Speed;
                    projectile.lifetime = lifetime;
                    projectile.homing = request.EnableHoming;

                    if (target != null)
                    {
                        projectile.currentTarget = target;
                    }

                    // Set player/enemy state
                    projectile.isPlayerShot = poolType == ProjectilePoolType.PlayerBasic;

                    // Ensure job system is updated with initial state
                    if (projectileJobSystem != null)
                    {
                        projectileJobSystem.UpdateProjectileMovementData(
                            projectile.projectileIndex,
                            position,
                            rotation,
                            rotation * Vector3.forward * request.Speed,
                            target != null ? target.position : position + rotation * Vector3.forward * 100f,
                            request.EnableHoming,
                            10f, // Default rotate speed
                            request.Speed,
                            1f,  // Default time scale
                            lifetime
                        );
                    }
                }

                return projectile;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileManager] Error spawning projectile: {e}");
                return null;
            }
        }

        private void FixedUpdate()
        {
            if (!IsInitialized || !isClockInitialized || projectileJobSystem == null) return;

            try
            {
                // Use scaled fixed delta time
                float timeScale = globalClock != null ? globalClock.localTimeScale : 1f;
                float scaledFixedDeltaTime = Time.fixedDeltaTime * timeScale;
                projectileJobSystem.ScheduleProjectileMovement(scaledFixedDeltaTime);
                projectileJobSystem.CompleteProjectileUpdate();

                // Apply results to all projectiles with time scaling
                for (int i = 0; i < MAX_PROJECTILES; i++)
                {
                    if (projectileArray[i] != null && projectileArray[i].gameObject.activeInHierarchy && projectileArray[i]._movement != null)
                    {
                        Vector3 newPosition = projectileJobSystem.GetProjectilePosition(i);
                        Quaternion newRotation = projectileJobSystem.GetProjectileRotation(i);
                        Vector3 newVelocity = projectileJobSystem.GetProjectileVelocity(i);

                        // Don't scale velocity here - it's already scaled in the job
                        projectileArray[i]._movement.ApplyJobResults(newPosition, newRotation, newVelocity);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileManager] Error in FixedUpdate: {e.Message}");
            }
        }

        private void OnDestroy()
        {
            IsInitialized = false;
            if (projectileGrid != null && nearbyProjectilesList != null)
            {
                projectileGrid.ReturnResultsList(nearbyProjectilesList);
            }

            if (Koreographer.Instance != null)
            {
                Koreographer.Instance.UnregisterForEvents(coordinatedAttackEventID, OnCoordinatedAttackBeat);
            }
        }

        public void ClearAllProjectiles()
        {
            Debug.Log($"[{GetType().Name}] Clearing all projectiles");

            // Complete any running jobs first
            if (projectileJobSystem != null)
            {
                projectileJobSystem.CompleteProjectileUpdate();
            }

            // Clear all arrays
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (activeSlots[i] && projectileArray[i] != null)
                {
                    // Properly clean up projectile
                    projectileJobSystem?.DeactivateProjectile(i);
                    projectileGrid?.RemoveProjectile(projectileArray[i]);
                    projectileArray[i] = null;
                }
                activeSlots[i] = false;
            }

            // Reset counts
            playerProjectileCount = 0;
            enemyProjectileCount = 0;
            Array.Clear(playerProjectileArray, 0, MAX_PROJECTILES);
            Array.Clear(enemyProjectileArray, 0, MAX_PROJECTILES);

            // Clear grid and other collections
            projectileGrid?.ClearGrids();
            homingProjectileIds.Clear();
            lastPositions.Clear();
        }

        public void ReRegisterEnemiesAndProjectiles()
        {
            ClearAllProjectiles();
            GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
            foreach (GameObject enemy in enemies)
            {
                var enemyCore = enemy.GetComponent<EnemyCore>();
                if (enemyCore != null)
                {
                    enemyCore.RegisterProjectiles();
                }
            }
        }

        private int GetLayerMaskForProjectile(ProjectileStateBased projectile)
        {
            return projectile.isPlayerShot ? enemyLayerMask : playerLayerMask;
        }

        public ProjectileStateBased GetProjectileById(int id)
        {
            if (id < 0 || id >= MAX_PROJECTILES || !activeSlots[id] || projectileArray[id] == null)
            {
                return null;
            }
            return projectileArray[id];
        }

        private void InitializeTrailPool()
        {
            GameObject trailContainer = new GameObject("PlayerShotTrails");
            trailContainer.transform.parent = transform;

            for (int i = 0; i < INITIAL_TRAIL_POOL_SIZE; i++)
            {
                GameObject trailObj = new GameObject($"Trail_{i}");
                trailObj.transform.parent = trailContainer.transform;

                LineRenderer line = trailObj.AddComponent<LineRenderer>();
                line.material = trailMaterial;
                line.startWidth = trailStartWidth;
                line.endWidth = trailEndWidth;
                line.positionCount = 2;
                line.useWorldSpace = true;
                line.gameObject.SetActive(false);

                trailPool.Enqueue(line);
            }
        }

        private LineRenderer GetTrailFromPool()
        {
            if (trailPool.Count == 0)
            {
                // Create new trail if pool is empty
                GameObject trailObj = new GameObject("Trail_Dynamic");
                trailObj.transform.parent = transform.Find("PlayerShotTrails");

                LineRenderer line = trailObj.AddComponent<LineRenderer>();
                line.material = trailMaterial;
                line.startWidth = trailStartWidth;
                line.endWidth = trailEndWidth;
                line.positionCount = 2;
                line.useWorldSpace = true;
                return line;
            }

            LineRenderer trail = trailPool.Dequeue();
            trail.gameObject.SetActive(true);
            return trail;
        }

        private void ReturnTrailToPool(LineRenderer trail)
        {
            trail.gameObject.SetActive(false);
            trailPool.Enqueue(trail);
        }

        public void CreatePlayerShotTrail(Vector3 startPosition, Vector3 targetPosition)
        {
            LineRenderer trail = GetTrailFromPool();

            trail.SetPosition(0, startPosition);
            trail.SetPosition(1, startPosition);

            PlayerShotTrail trailData = new PlayerShotTrail
            {
                lineRenderer = trail,
                startTime = Time.time, // Use regular time for trail start
                duration = trailDuration,
                startPos = startPosition,
                endPos = targetPosition,
                startColor = trailStartColor,
                endColor = trailEndColor
            };

            activeTrails.Add(trailData);
        }

        private void UpdateTrails()
        {
            float currentTimeScale = globalClock != null ? globalClock.localTimeScale : 1f;

            for (int i = activeTrails.Count - 1; i >= 0; i--)
            {
                PlayerShotTrail trail = activeTrails[i];
                // Use scaled time for trail progress
                float elapsed = (Time.time - trail.startTime) * currentTimeScale;
                float progress = elapsed / (trail.duration / currentTimeScale);

                if (progress >= 1f)
                {
                    ReturnTrailToPool(trail.lineRenderer);
                    activeTrails.RemoveAt(i);
                    continue;
                }

                // Update trail position
                Vector3 currentEnd = Vector3.Lerp(trail.startPos, trail.endPos, progress);
                trail.lineRenderer.SetPosition(1, currentEnd);

                // Update trail color and opacity
                Color currentColor = Color.Lerp(trail.startColor, trail.endColor, progress);
                trail.lineRenderer.startColor = trail.startColor;
                trail.lineRenderer.endColor = currentColor;
            }
        }

        private void UpdateGroupAttack()
        {
            if (!enableGroupAttacks) return;

            if (groupAttackQueue.Count < groupAttackConfig.minProjectilesForGroup)
            {
                // Check for new projectiles to add to queue
                for (int i = 0; i < enemyProjectileCount; i++)
                {
                    ProjectileStateBased projectile = enemyProjectileArray[i];
                    if (projectile != null && !projectile.isCoordinated && projectile.currentTarget != null)
                    {
                        float distanceToTarget = Vector3.Distance(projectile.transform.position, projectile.currentTarget.position);
                        if (distanceToTarget <= groupAttackConfig.groupAttackRange)
                        {
                            projectile.isCoordinated = true;
                            projectile.PauseLifetime();
                            groupAttackQueue.Enqueue(projectile);

                            // If this is the first projectile, start the group attack timer
                            if (groupAttackQueue.Count == 1)
                            {
                                groupAttackStartTime = Time.time;
                                Debug.Log($"[GroupAttack] Starting new group attack with {groupAttackQueue.Count} projectiles");
                            }

                            // Add visual effect
                            if (projectile.TryGetComponent<Renderer>(out var renderer))
                            {
                                projectile.originalColor = renderer.material.color;
                                renderer.material.color = groupAttackConfig.groupAttackColor;
                            }
                        }
                    }
                }
            }
            else if (groupAttackQueue.Count > 0)
            {
                var player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    float timeInGroup = Time.time - groupAttackStartTime;

                    // Position projectiles in a circle around the player
                    int index = 0;
                    foreach (var projectile in groupAttackQueue)
                    {
                        if (projectile != null && projectile.gameObject.activeInHierarchy)
                        {
                            // Calculate position in circle
                            float angle = (2f * Mathf.PI * index) / groupAttackQueue.Count;
                            Vector3 offset = new Vector3(
                                Mathf.Cos(angle) * groupAttackConfig.groupAttackDistance,
                                0f,
                                Mathf.Sin(angle) * groupAttackConfig.groupAttackDistance
                            );

                            // Move projectile to position
                            Vector3 targetPos = player.transform.position + offset;
                            Vector3 moveDir = (targetPos - projectile.transform.position).normalized;
                            float moveSpeed = projectile.bulletSpeed * groupAttackConfig.groupAttackSpeed * Time.deltaTime;
                            projectile.transform.position += moveDir * moveSpeed;

                            // Face player
                            projectile.transform.forward = (player.transform.position - projectile.transform.position).normalized;
                            index++;
                        }
                    }

                    // Start attacking after delay
                    if (timeInGroup >= groupAttackConfig.groupAttackDelay)
                    {
                        var projectile = groupAttackQueue.Dequeue();
                        if (projectile != null && projectile.gameObject.activeInHierarchy)
                        {
                            projectile.isCoordinated = false;
                            projectile.ResumeLifetime();

                            // Restore original color
                            if (projectile.TryGetComponent<Renderer>(out var renderer))
                            {
                                renderer.material.color = projectile.originalColor;
                            }

                            // Point at player
                            projectile.transform.forward = (player.transform.position - projectile.transform.position).normalized;
                        }
                    }
                }
            }
        }

        private void OnDrawGizmos()
        {
            if (!enableGroupAttacks || !groupAttackConfig?.showGroupDebugVisualization == true) return;

            var player = GameObject.FindGameObjectWithTag("Player");
            if (player == null) return;

            // Draw group attack range
            Gizmos.color = new Color(groupAttackConfig.groupAttackColor.r, groupAttackConfig.groupAttackColor.g, groupAttackConfig.groupAttackColor.b, 0.3f);
            Gizmos.DrawWireSphere(player.transform.position, groupAttackConfig.groupAttackRange);

            // Draw filled sphere
            Gizmos.color = new Color(groupAttackConfig.groupAttackColor.r, groupAttackConfig.groupAttackColor.g, groupAttackConfig.groupAttackColor.b, 0.1f);
            DrawSphere(player.transform.position, groupAttackConfig.groupAttackRange);

            if (Application.isPlaying && groupAttackQueue != null)
            {
                foreach (var projectile in groupAttackQueue)
                {
                    if (projectile != null && projectile.gameObject != null && projectile.gameObject.activeInHierarchy)
                    {
                        // Draw projectile position
                        Gizmos.color = groupAttackConfig.groupAttackColor;
                        DrawSphere(projectile.transform.position, 0.5f);

                        // Draw line to player
                        Gizmos.DrawLine(projectile.transform.position, player.transform.position);
                    }
                }
            }
        }

        // Helper method to draw a filled sphere
        private void DrawSphere(Vector3 center, float radius)
        {
            int segments = 16;
            int rings = 16;

            for (int i = 0; i <= rings; i++)
            {
                float phi = Mathf.PI * i / rings;
                for (int j = 0; j <= segments; j++)
                {
                    float theta = 2f * Mathf.PI * j / segments;

                    float x = radius * Mathf.Sin(phi) * Mathf.Cos(theta);
                    float y = radius * Mathf.Sin(phi) * Mathf.Sin(theta);
                    float z = radius * Mathf.Cos(phi);

                    Vector3 point = center + new Vector3(x, y, z);

                    if (i > 0 && j > 0)
                    {
                        Gizmos.DrawLine(point, center + new Vector3(
                            radius * Mathf.Sin(phi - Mathf.PI / rings) * Mathf.Cos(theta),
                            radius * Mathf.Sin(phi - Mathf.PI / rings) * Mathf.Sin(theta),
                            radius * Mathf.Cos(phi - Mathf.PI / rings)
                        ));
                    }

                    if (j > 0)
                    {
                        Gizmos.DrawLine(point, center + new Vector3(
                            radius * Mathf.Sin(phi) * Mathf.Cos(theta - 2f * Mathf.PI / segments),
                            radius * Mathf.Sin(phi) * Mathf.Sin(theta - 2f * Mathf.PI / segments),
                            radius * Mathf.Cos(phi)
                        ));
                    }
                }
            }
        }

        private void OnCoordinatedAttackBeat(KoreographyEvent evt)
        {
            // Keep this method for future implementation
            // Currently using group attack system instead
        }

        // Add method to predict target position using spline
        public Vector3 PredictTargetPositionOnSpline(Vector3 projectilePosition, float projectileSpeed, Vector3 currentTargetPosition)
        {
            if (playerSplineController == null || !playerSplineController.IsReady || playerSplineController.Spline == null)
            {
                return currentTargetPosition;
            }

            try
            {
                CurvySpline spline = playerSplineController.Spline;
                float playerSpeed = playerSplineController.Speed;
                float currentTF = playerSplineController.RelativePosition;

                // Get current player position and direction
                Vector3 playerPos = spline.transform.TransformPoint(spline.Interpolate(currentTF));
                Vector3 playerDir = spline.transform.TransformDirection(spline.GetTangent(currentTF)).normalized;

                // Calculate time until intersection using law of cosines
                Vector3 toPlayer = playerPos - projectilePosition;
                float distanceToPlayer = toPlayer.magnitude;
                Vector3 toPlayerDir = toPlayer / distanceToPlayer;

                // Calculate angle between player's movement and line to projectile
                float cosAngle = Vector3.Dot(playerDir, toPlayerDir);
                float angle = Mathf.Acos(cosAngle);

                // Calculate speed ratio to determine how far ahead to look
                float speedRatio = playerSpeed / projectileSpeed;

                // MODIFICATION: Reduce lookAheadMultiplier for close distances
                float distanceFactor = Mathf.Clamp01(distanceToPlayer / 20f); // Normalize distance up to 20 units

                // Add prediction dampening for close ranges (exponential falloff based on distance)
                // This creates a sharper reduction in prediction as we get closer
                float predictionDampening = 0.5f; // Default value

                // Try to get the actual projectile by checking for projectiles near the position
                var nearbyProjectiles = Physics.OverlapSphere(projectilePosition, 0.5f);
                foreach (var collider in nearbyProjectiles)
                {
                    if (collider.TryGetComponent<ProjectileStateBased>(out var projectile))
                    {
                        if (projectile.runtimeSteeringConfig != null)
                        {
                            predictionDampening = projectile.runtimeSteeringConfig.predictionDampening;
                            break;
                        }
                    }
                }

                // Apply dampening effect with distance-based falloff
                float dampening = Mathf.Pow(distanceFactor, 1.0f + predictionDampening);
                float lookAheadMultiplier = Mathf.Lerp(1.0f, 5.0f, dampening * speedRatio);

                // Law of cosines: c² = a² + b² - 2ab*cos(C)
                // c = projectileSpeed * t
                // b = playerSpeed * t
                // Therefore: (projectileSpeed * t)² = distanceToPlayer² + (playerSpeed * t)² - 2 * distanceToPlayer * playerSpeed * t * cos(angle)

                // Solve quadratic equation: at² + bt + c = 0
                float a = projectileSpeed * projectileSpeed - playerSpeed * playerSpeed;
                float b = 2 * distanceToPlayer * playerSpeed * cosAngle;
                float c = -distanceToPlayer * distanceToPlayer;

                // Quadratic formula: t = (-b ± √(b² - 4ac)) / (2a)
                float discriminant = b * b - 4 * a * c;
                if (discriminant < 0)
                {
                    // No solution - player is moving away faster than projectile can catch up
                    return currentTargetPosition;
                }

                float t = (-b + Mathf.Sqrt(discriminant)) / (2 * a);
                if (t < 0)
                {
                    // Negative time - use the other solution
                    t = (-b - Mathf.Sqrt(discriminant)) / (2 * a);
                }

                // Calculate how far the player will travel in this time
                float distancePlayerTravels = playerSpeed * t * lookAheadMultiplier;

                // Get current curvature to adjust prediction
                float nextTF = Mathf.Min(1f, currentTF + 0.01f);
                Vector3 currentTangent = spline.GetTangent(currentTF);
                Vector3 nextTangent = spline.GetTangent(nextTF);
                float curvature = Vector3.Angle(currentTangent, nextTangent) / 0.01f;

                // MODIFICATION: Reduce curvature compensation for close distances
                float distanceScale = Mathf.Clamp01(distanceToPlayer / 10f);
                float curvatureMultiplier = 1f + Mathf.Abs(curvature) * 0.5f * distanceScale;
                distancePlayerTravels *= curvatureMultiplier;

                // Calculate the predicted position on the spline
                float predictedTF = currentTF + (distancePlayerTravels / spline.Length);

                // Handle closed splines
                if (spline.Closed)
                {
                    predictedTF = predictedTF % 1f;
                }
                else
                {
                    predictedTF = Mathf.Clamp01(predictedTF);
                }

                // Get the predicted world position
                Vector3 predictedPosition = spline.transform.TransformPoint(spline.Interpolate(predictedTF));

                // Additional safety check: if the predicted position would cause extreme steering
                // (like swerving away from a direct hit), blend toward direct position
                float angleToCurrentPosition = Vector3.Angle(toPlayerDir, (predictedPosition - projectilePosition).normalized);
                if (angleToCurrentPosition > 60f && distanceToPlayer < 10f)
                {
                    float angleFactor = Mathf.Clamp01((angleToCurrentPosition - 60f) / 30f);
                    float blendToDirectFactor = angleFactor * (1f - distanceFactor);
                    predictedPosition = Vector3.Lerp(predictedPosition, playerPos, blendToDirectFactor);
                }

                return predictedPosition;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileManager] Error predicting spline position: {e.Message}\n{e.StackTrace}");
                return currentTargetPosition;
            }
        }

        // Get the player's eye level height for projectile approach calculations
        public float GetPlayerEyeLevel()
        {
            // Try to find the player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                return 0f;
            }

            // Get player's position
            Vector3 playerPosition = player.transform.position;

            // Get player's height by checking for a character controller or collider
            float playerHeight = 2f; // Default fallback height

            // Try to get player height from collider bounds
            Collider playerCollider = player.GetComponent<Collider>();
            if (playerCollider != null)
            {
                playerHeight = playerCollider.bounds.size.y;
            }

            // Eye level is typically around 90% of character height
            float eyeLevel = playerPosition.y + (playerHeight * 0.9f);

            // Cache the eye level value for efficiency if needed more frequently
            return eyeLevel;
        }

        // Adjust target position to approach from player eye level
        public Vector3 AdjustForVerticalApproach(Vector3 projectilePosition, Vector3 targetPosition, SteeringConfig steeringConfig)
        {
            if (steeringConfig == null || !steeringConfig.usePlayerEyeLevelApproach)
            {
                return targetPosition;
            }

            // Get player eye level
            float playerEyeLevel = GetPlayerEyeLevel();

            // Apply the configured offset
            float approachHeight = playerEyeLevel + steeringConfig.verticalApproachOffset;

            // Get distance to target
            float distanceToTarget = Vector3.Distance(projectilePosition, targetPosition);

            // Calculate strength factor based on distance
            float distanceFactor = Mathf.Clamp01(distanceToTarget / steeringConfig.verticalApproachDistance);

            // Apply vertical approach only when outside of inner arrival distance
            if (distanceToTarget > steeringConfig.innerArrivalDistance)
            {
                // Create a new position with adjusted height
                Vector3 adjustedPosition = targetPosition;

                // Apply height adjustment (weighted by distance and strength)
                float heightBlend = Mathf.Lerp(0, steeringConfig.verticalPathStrength, distanceFactor);
                adjustedPosition.y = Mathf.Lerp(targetPosition.y, approachHeight, heightBlend);

                return adjustedPosition;
            }

            return targetPosition;
        }
    }
}
