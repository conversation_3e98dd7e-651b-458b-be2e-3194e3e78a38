using UnityEngine;
using BTR;
using Chronos;
using System.Collections.Generic;
using PathologicalGames;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Collections;
using BTR.Radar;  // Add namespace for radar components
using BTR.Projectiles;  // Add namespace for new projectile system

namespace BTR
{
    public struct ProjectileSpawnRequest
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public float Speed;
        public float Lifetime;
        public float Scale;
        public float Damage;
        public bool EnableHoming;
        public Transform Target;
        public bool IsStatic;
        public int MaterialId;
        public string ClockKey;  // For Chronos timeline management
        public float Accuracy;   // For projectile accuracy/spread
    }

    [DefaultExecutionOrder(-300)]
    public class ProjectilePool : MonoBehaviour
    {
        public static ProjectilePool Instance { get; private set; }

        [Header("Projectile Prefabs")]
        [SerializeField] private GameObject legacyProjectilePrefab;  // ProjectileStateBased prefab (for backward compatibility)
        [SerializeField] private GameObject newProjectilePrefab;     // ProjectileEntity prefab (new system)

        [Header("System Settings")]
        [SerializeField] private bool preferNewSystem = false;       // Which system to use by default
        [SerializeField] private int initialPoolSize = 350;

        [Header("Dynamic Pool Settings")]
        [SerializeField] private bool enableDynamicPooling = true;
        [SerializeField] private float growthThreshold = 0.9f;
        [SerializeField] private float shrinkThreshold = 0.4f;
        [SerializeField] private int growthAmount = 50;
        [SerializeField] private float poolCheckInterval = 1f;

        [Header("Timeline Settings")]
        [SerializeField] private int maxTimelineInitsPerFrame = 1;
        [SerializeField] private float timelineInitDelay = 0.1f;

        private const int MAX_POOL_SIZE = 1000;
        private const int COMMON_PROJECTILE_COUNT = 50;

        private readonly object poolLock = new object();
        private readonly Queue<ProjectileStateBased> projectilePool = new Queue<ProjectileStateBased>();
        private readonly HashSet<ProjectileStateBased> activeProjectiles = new HashSet<ProjectileStateBased>();

        // New system pool management
        private readonly Queue<ProjectileEntity> newProjectilePool = new Queue<ProjectileEntity>();
        private readonly HashSet<ProjectileEntity> activeNewProjectiles = new HashSet<ProjectileEntity>();

        private readonly ConcurrentQueue<ProjectileSpawnRequest> projectileRequestQueue = new ConcurrentQueue<ProjectileSpawnRequest>();

        private Transform projectileContainer;
        private bool isInitialized;
        private int totalProjectilesCreated;
        private float lastPoolCheckTime;
        private float lastPreWarmTime;
        private int peakActiveCount;
        private float poolUtilization;

        private int timelineInitsThisFrame;
        private float lastTimelineInitTime;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            // Validate projectile prefabs - need at least one
            if (legacyProjectilePrefab == null && newProjectilePrefab == null)
            {
                Debug.LogError("[ProjectilePool] No projectile prefabs assigned! Please assign at least one prefab (legacy or new).");
                enabled = false;
                return;
            }

            // Auto-configure system preference based on available prefabs
            if (legacyProjectilePrefab != null && newProjectilePrefab == null)
            {
                // Only legacy prefab - use legacy system
                preferNewSystem = false;
                Debug.Log("[ProjectilePool] Only legacy prefab assigned - using legacy system");
            }
            else if (legacyProjectilePrefab == null && newProjectilePrefab != null)
            {
                // Only new prefab - use new system
                preferNewSystem = true;
                Debug.Log("[ProjectilePool] Only new prefab assigned - using new system");
            }
            else if (legacyProjectilePrefab != null && newProjectilePrefab != null)
            {
                // Both prefabs available - use user preference
                Debug.Log($"[ProjectilePool] Both prefabs assigned - using {(preferNewSystem ? "new" : "legacy")} system");
            }

            // Create projectile container
            projectileContainer = new GameObject("ProjectileContainer").transform;
            projectileContainer.SetParent(transform);

            InitializePool();
        }

        private void OnDestroy()
        {
            lock (poolLock)
            {
                ClearPool();
                Instance = null;
            }
        }

        private void InitializePool()
        {
            lock (poolLock)
            {
                if (isInitialized) return;

                try
                {
                    for (int i = 0; i < initialPoolSize; i++)
                    {
                        CreatePooledProjectile();
                    }
                    isInitialized = true;
                    Debug.Log($"[ProjectilePool] Initialized pool with {initialPoolSize} projectiles using {(preferNewSystem ? "new" : "legacy")} system");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ProjectilePool] Error initializing pool: {e.Message}");
                    ClearPool();
                    throw;
                }
            }
        }

        /// <summary>
        /// Create a pooled projectile using the unified interface approach
        /// </summary>
        private IProjectile CreatePooledProjectile()
        {
            GameObject prefabToUse = GetPreferredPrefab();
            if (prefabToUse == null)
            {
                Debug.LogError("[ProjectilePool] No projectile prefab available for pool creation!");
                return null;
            }

            try
            {
                // Create the projectile in an inactive state
                GameObject projectileObj = Instantiate(prefabToUse, projectileContainer);
                projectileObj.SetActive(false);

                // Set the projectile layer
                projectileObj.layer = LayerMask.NameToLayer("Projectile");

                // Get the IProjectile interface
                IProjectile projectile = ProjectileSystemBridge.GetProjectileInterface(projectileObj);
                if (projectile == null)
                {
                    Debug.LogError($"[ProjectilePool] Failed to get IProjectile interface from {prefabToUse.name}");
                    Destroy(projectileObj);
                    return null;
                }

                // Initialize the projectile
                try
                {
                    if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
                    {
                        // For legacy projectiles, use traditional initialization
                        var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                        var legacyProjectile = adapter?.GetProjectileStateBased();
                        if (legacyProjectile != null)
                        {
                            legacyProjectile.InitializeProjectile();

                            // Add to legacy pool
                            lock (poolLock)
                            {
                                projectilePool.Enqueue(legacyProjectile);
                                totalProjectilesCreated++;
                            }
                        }
                    }
                    else
                    {
                        // For new system projectiles, use entity initialization
                        var entity = projectile as ProjectileEntity;
                        if (entity != null)
                        {
                            entity.Initialize();

                            // Add to new system pool
                            lock (poolLock)
                            {
                                newProjectilePool.Enqueue(entity);
                                totalProjectilesCreated++;
                            }

                            Debug.Log($"[ProjectilePool] Created new system projectile: {entity.EntityID}");
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ProjectilePool] Error initializing projectile: {e.Message}");
                    Destroy(projectileObj);
                    return null;
                }

                return projectile;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error creating pooled projectile: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get the preferred prefab based on system settings
        /// </summary>
        private GameObject GetPreferredPrefab()
        {
            if (preferNewSystem && newProjectilePrefab != null)
                return newProjectilePrefab;
            else if (legacyProjectilePrefab != null)
                return legacyProjectilePrefab;
            else if (newProjectilePrefab != null)
                return newProjectilePrefab;

            return null;
        }

        private ProjectileStateBased CreateNewProjectile(bool initializeTimeline = true)
        {
            // Try to get legacy prefab first
            var legacyPrefabComponent = GetLegacyPrefabComponent();
            if (legacyPrefabComponent != null)
            {
                // Use legacy prefab if available
                return CreateLegacyProjectile(legacyPrefabComponent, initializeTimeline);
            }
            else if (newProjectilePrefab != null)
            {
                // Fall back to creating a legacy-compatible projectile from new system
                return CreateLegacyFromNewSystem();
            }
            else
            {
                Debug.LogError("[ProjectilePool] No projectile prefabs available for CreateNewProjectile!");
                return null;
            }
        }

        private ProjectileStateBased CreateLegacyProjectile(ProjectileStateBased prefabToUse, bool initializeTimeline = true)
        {
            try
            {
                // Create the projectile in an inactive state
                var projectile = Instantiate(prefabToUse, projectileContainer);
                if (projectile == null)
                {
                    Debug.LogError("[ProjectilePool] Failed to instantiate projectile!");
                    return null;
                }

                projectile.gameObject.SetActive(false);

                // Set the projectile layer
                projectile.gameObject.layer = LayerMask.NameToLayer("Projectile");

                // Initialize the projectile
                try
                {
                    projectile.InitializeProjectile();
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ProjectilePool] Error initializing projectile components: {e.Message}");
                    Destroy(projectile.gameObject);
                    return null;
                }

                // Initialize timeline if needed
                if (initializeTimeline)
                {
                    StartCoroutine(InitializeTimeline(projectile));
                }

                lock (poolLock)
                {
                    projectilePool.Enqueue(projectile);
                    totalProjectilesCreated++;
                }

                return projectile;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error creating new projectile: {e.Message}");
                return null;
            }
        }

        public ProjectileStateBased GetProjectile()
        {
            // If only new system is available, guide user to use the unified interface
            if (legacyProjectilePrefab == null && newProjectilePrefab != null)
            {
                Debug.LogWarning("[ProjectilePool] GetProjectile() called but only new system prefab is available. " +
                               "Consider using GetProjectileInterface() for better compatibility with both systems.");
                // Still try to return something if possible, but it will likely be null
            }

            ProjectileStateBased projectile = null;

            lock (poolLock)
            {
                while (projectilePool.Count > 0 && projectile == null)
                {
                    projectile = projectilePool.Dequeue();
                    if (projectile == null) continue;
                }

                if (projectile == null && totalProjectilesCreated < MAX_POOL_SIZE)
                {
                    projectile = CreateNewProjectile();
                }

                if (projectile == null)
                {
                    Debug.LogWarning("[ProjectilePool] No available projectiles and max pool size reached.");
                    return null;
                }

                activeProjectiles.Add(projectile);
            }

            try
            {
                ResetProjectile(projectile);
                projectile.gameObject.SetActive(true);
                return projectile;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error preparing projectile: {e.Message}");
                ReturnProjectileToPool(projectile);
                return null;
            }
        }

        private void ResetProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            try
            {
                // Clean up old job system index first
                if (projectile.projectileIndex >= 0 && ProjectileManager.Instance != null)
                {
                    var jobSystem = ProjectileManager.Instance.GetProjectileJobSystem();
                    if (jobSystem != null)
                    {
                        jobSystem.DeactivateProjectile(projectile.projectileIndex);
                        projectile.projectileIndex = -1;  // Clear the old index
                    }
                }

                if (projectile.rb != null)
                {
                    projectile.rb.isKinematic = false;
                    projectile.rb.linearVelocity = Vector3.zero;
                    projectile.rb.angularVelocity = Vector3.zero;
                }

                projectile.transform.localScale = Vector3.one;
                projectile.transform.rotation = Quaternion.identity;
                projectile.transform.position = Vector3.zero;
                projectile.ResetForPool();

                // Initialize new movement component with fresh index
                if (ProjectileManager.Instance != null)
                {
                    var jobSystem = ProjectileManager.Instance.GetProjectileJobSystem();
                    if (jobSystem != null)
                    {
                        projectile.projectileIndex = ProjectileManager.Instance.GetNextProjectileIndex();
                        projectile._movement = new ProjectileMovement(projectile, jobSystem, projectile.projectileIndex);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error in ResetProjectile: {e.Message}");
            }
        }

        public void ReturnProjectileToPool(ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            try
            {
                // Ensure projectile is inactive first to prevent any state changes during cleanup
                projectile.gameObject.SetActive(false);

                // Unregister from radar first if it's homing
                if (projectile.homing && ProjectileManager.Instance != null)
                {
                    ProjectileManager.Instance.UnregisterHomingProjectile(projectile.GetInstanceID());
                }

                // Clean up job system state
                if (projectile.projectileIndex >= 0 && ProjectileManager.Instance != null)
                {
                    var jobSystem = ProjectileManager.Instance?.GetProjectileJobSystem();
                    if (jobSystem != null)
                    {
                        jobSystem.CompleteProjectileUpdate();  // Ensure job system is ready
                        jobSystem.DeactivateProjectile(projectile.projectileIndex);
                        projectile.projectileIndex = -1;  // Clear the index
                    }
                }

                // Unregister from ProjectileManager after job system cleanup
                if (ProjectileManager.Instance != null)
                {
                    ProjectileManager.Instance.UnregisterProjectile(projectile);
                }

                // Reset the projectile state
                ResetProjectile(projectile);

                lock (poolLock)
                {
                    activeProjectiles.Remove(projectile);
                    if (!projectilePool.Contains(projectile))
                    {
                        projectilePool.Enqueue(projectile);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error returning projectile to pool: {e.Message}");
            }
        }

        private void Update()
        {
            if (enableDynamicPooling && Time.time - lastPoolCheckTime > poolCheckInterval)
            {
                UpdatePoolSize();
                lastPoolCheckTime = Time.time;
            }

            lock (poolLock)
            {
                int activeCount = activeProjectiles.Count;
                peakActiveCount = Mathf.Max(peakActiveCount, activeCount);
                poolUtilization = (float)activeCount / (projectilePool.Count + activeCount);
            }

            ProcessProjectileRequests();
        }

        private void ProcessProjectileRequests()
        {
            const int MAX_REQUESTS_PER_FRAME = 5;
            int processedCount = 0;

            while (processedCount < MAX_REQUESTS_PER_FRAME && projectileRequestQueue.TryDequeue(out var request))
            {
                var projectile = GetProjectile(request);
                if (projectile != null)
                {
                    processedCount++;
                }
            }
        }

        private void UpdatePoolSize()
        {
            lock (poolLock)
            {
                if (poolUtilization > growthThreshold && totalProjectilesCreated < MAX_POOL_SIZE)
                {
                    int growthSize = Mathf.Min(growthAmount, MAX_POOL_SIZE - totalProjectilesCreated);
                    StartCoroutine(GrowPool(growthSize));
                }
                else if (poolUtilization < shrinkThreshold && projectilePool.Count > initialPoolSize)
                {
                    int shrinkAmount = Mathf.FloorToInt(projectilePool.Count * 0.2f);
                    ShrinkPool(shrinkAmount);
                }
            }
        }

        private System.Collections.IEnumerator GrowPool(int amount)
        {
            for (int i = 0; i < amount; i++)
            {
                if (Time.deltaTime < 0.033f)
                {
                    CreateNewProjectile(false);
                    yield return new WaitForSeconds(0.02f);
                }
                else
                {
                    yield return new WaitForSeconds(0.05f);
                }
            }
        }

        private void ShrinkPool(int amount)
        {
            for (int i = 0; i < amount && projectilePool.Count > initialPoolSize; i++)
            {
                if (projectilePool.Count > 0)
                {
                    var projectile = projectilePool.Dequeue();
                    if (projectile != null)
                    {
                        Destroy(projectile.gameObject);
                        totalProjectilesCreated--;
                    }
                }
            }
        }

        private void PreWarmCommonProjectiles()
        {
            if (projectilePool.Count < COMMON_PROJECTILE_COUNT)
            {
                int amountToPreWarm = COMMON_PROJECTILE_COUNT - projectilePool.Count;
                StartCoroutine(GrowPool(amountToPreWarm));
            }
        }

        private System.Collections.IEnumerator InitializeTimeline(ProjectileStateBased projectile)
        {
            if (projectile == null) yield break;

            if (timelineInitsThisFrame >= maxTimelineInitsPerFrame)
            {
                yield return new WaitForEndOfFrame();
                timelineInitsThisFrame = 0;
            }

            float timeSinceLastInit = Time.time - lastTimelineInitTime;
            if (timeSinceLastInit < timelineInitDelay)
            {
                yield return new WaitForSeconds(timelineInitDelay - timeSinceLastInit);
            }

            Timeline timeline = projectile.gameObject.GetComponent<Timeline>();
            if (timeline != null)
            {
                timeline.enabled = false;
                timeline.rewindable = true;

                var cachedActive = projectile.gameObject.activeSelf;
                projectile.transform.gameObject.SetActive(true);
                timeline.enabled = true;
                projectile.transform.gameObject.SetActive(cachedActive);
            }

            timelineInitsThisFrame++;
            lastTimelineInitTime = Time.time;
        }

        public ProjectileStateBased GetProjectile(ProjectileSpawnRequest request)
        {
            ProjectileStateBased projectile = GetProjectile();
            if (projectile != null)
            {
                try
                {
                    // Ensure projectile is inactive during setup
                    projectile.gameObject.SetActive(false);

                    // Setup basic properties
                    projectile.transform.position = request.Position;
                    projectile.transform.rotation = request.Rotation;
                    projectile.transform.localScale = Vector3.one * request.Scale;

                    // Initialize the projectile first
                    projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, request.Target);

                    // Ensure we have a valid job system index
                    if (projectile.projectileIndex < 0 && ProjectileManager.Instance != null)
                    {
                        projectile.projectileIndex = ProjectileManager.Instance.GetNextProjectileIndex();
                    }

                    // Initialize movement with job system
                    var jobSystem = ProjectileManager.Instance?.GetProjectileJobSystem();
                    if (jobSystem != null)
                    {
                        jobSystem.CompleteProjectileUpdate();  // Ensure job system is ready
                        jobSystem.UpdateProjectileMovementData(
                            projectile.projectileIndex,
                            request.Position,
                            request.Rotation,
                            request.Rotation * Vector3.forward * request.Speed,
                            request.Target != null ? request.Target.position : request.Position + request.Rotation * Vector3.forward * 100f,
                            request.EnableHoming,
                            10f, // Default rotate speed
                            request.Speed,
                            1f,  // Default time scale
                            request.Lifetime
                        );
                    }

                    // Set rigidbody properties
                    if (projectile.rb != null)
                    {
                        projectile.rb.isKinematic = false;
                        projectile.rb.linearVelocity = request.Rotation * Vector3.forward * request.Speed;
                    }

                    // Register with managers
                    if (ProjectileManager.Instance != null)
                    {
                        ProjectileManager.Instance.RegisterProjectile(projectile);
                    }

                    // Register with tracking manager (handles both homing and radar registration)
                    if (ProjectileTrackingManager.Instance != null)
                    {
                        ProjectileTrackingManager.Instance.RegisterProjectile(projectile);
                    }

                    // Now activate the projectile
                    projectile.gameObject.SetActive(true);

                    return projectile;
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ProjectilePool] Error setting up projectile: {e}");
                    ReturnProjectileToPool(projectile);
                    return null;
                }
            }
            return null;
        }

        public void EnqueueProjectileRequest(ProjectileSpawnRequest request)
        {
            projectileRequestQueue.Enqueue(request);
        }

        public bool TryDequeueProjectileRequest(out ProjectileSpawnRequest request)
        {
            return projectileRequestQueue.TryDequeue(out request);
        }

        #region New Dual-System Support Methods
        /// <summary>
        /// Get a projectile using the unified IProjectile interface (supports both systems)
        /// </summary>
        public IProjectile GetProjectileInterface()
        {
            if (preferNewSystem && newProjectilePrefab != null)
            {
                return GetNewProjectile();
            }
            else
            {
                return GetLegacyProjectileInterface();
            }
        }

        /// <summary>
        /// Get a projectile interface and set it up with the provided request
        /// </summary>
        public IProjectile GetProjectileInterface(ProjectileSpawnRequest request)
        {
            var projectile = GetProjectileInterface();
            if (projectile != null)
            {
                SetupProjectileInterface(projectile, request);
            }
            return projectile;
        }

        /// <summary>
        /// Return a projectile using the unified interface
        /// </summary>
        public void ReturnProjectileInterface(IProjectile projectile)
        {
            if (projectile == null) return;

            if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // Handle legacy projectile return
                var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                if (adapter != null)
                {
                    ReturnProjectileToPool(adapter.GetProjectileStateBased());
                }
            }
            else
            {
                // Handle new system projectile return using proper pooling
                var entity = projectile as ProjectileEntity;
                if (entity != null)
                {
                    ReturnNewProjectileToPool(entity);
                }
            }
        }

        private IProjectile GetNewProjectile()
        {
            if (newProjectilePrefab == null) return null;

            ProjectileEntity entity = null;

            lock (poolLock)
            {
                // Try to get from pool first
                while (newProjectilePool.Count > 0 && entity == null)
                {
                    entity = newProjectilePool.Dequeue();
                    if (entity == null || entity.GameObject == null)
                    {
                        entity = null;
                        continue;
                    }
                }

                // If no pooled projectile available, create a new one
                if (entity == null && totalProjectilesCreated < MAX_POOL_SIZE)
                {
                    entity = CreateNewProjectileEntity();
                }

                if (entity == null)
                {
                    Debug.LogWarning("[ProjectilePool] No available new projectiles and max pool size reached.");
                    return null;
                }

                activeNewProjectiles.Add(entity);
            }

            try
            {
                // Reset the projectile BEFORE it gets configured by the spawner
                ResetNewProjectile(entity);
                // Don't activate yet - let the spawner handle activation after setup
                return entity;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error preparing new projectile: {e.Message}");
                ReturnNewProjectileToPool(entity);
                return null;
            }
        }

        /// <summary>
        /// Create a new ProjectileEntity for the pool
        /// </summary>
        private ProjectileEntity CreateNewProjectileEntity()
        {
            if (newProjectilePrefab == null) return null;

            try
            {
                GameObject projectileObj = Instantiate(newProjectilePrefab, projectileContainer);
                projectileObj.SetActive(false);
                projectileObj.layer = LayerMask.NameToLayer("Projectile");

                var entity = projectileObj.GetComponent<ProjectileEntity>();
                if (entity == null)
                {
                    Debug.LogError("[ProjectilePool] New projectile prefab doesn't have ProjectileEntity component!");
                    Destroy(projectileObj);
                    return null;
                }

                // Ensure radar tracker is added for radar integration
                if (projectileObj.GetComponent<ProjectileRadarTracker>() == null)
                {
                    projectileObj.AddComponent<ProjectileRadarTracker>();
                    Debug.Log($"[ProjectilePool] Added ProjectileRadarTracker to new projectile: {entity.EntityID}");
                }

                entity.Initialize();
                totalProjectilesCreated++;

                Debug.Log($"[ProjectilePool] Created new ProjectileEntity: {entity.EntityID}");
                return entity;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error creating new ProjectileEntity: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Reset a new system projectile for reuse
        /// </summary>
        private void ResetNewProjectile(ProjectileEntity entity)
        {
            if (entity == null) return;

            try
            {
                Debug.Log($"[ProjectilePool] Resetting projectile {entity.EntityID} - current position: {entity.Transform.position}");

                // Reset transform
                entity.Transform.position = Vector3.zero;
                entity.Transform.rotation = Quaternion.identity;
                entity.Transform.localScale = Vector3.one;

                // Reset entity state
                entity.Deactivate();
                entity.ResetForPool();

                Debug.Log($"[ProjectilePool] Reset new projectile: {entity.EntityID} to position: {entity.Transform.position}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error resetting new projectile: {e.Message}");
            }
        }

        /// <summary>
        /// Return a new system projectile to the pool
        /// </summary>
        private void ReturnNewProjectileToPool(ProjectileEntity entity)
        {
            if (entity == null) return;

            try
            {
                entity.GameObject.SetActive(false);
                entity.Deactivate();

                lock (poolLock)
                {
                    activeNewProjectiles.Remove(entity);
                    if (!newProjectilePool.Contains(entity))
                    {
                        newProjectilePool.Enqueue(entity);
                    }
                }

                Debug.Log($"[ProjectilePool] Returned new projectile to pool: {entity.EntityID}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error returning new projectile to pool: {e.Message}");
            }
        }

        private IProjectile GetLegacyProjectileInterface()
        {
            var legacyProjectile = GetProjectile(); // Use existing legacy method
            if (legacyProjectile != null)
            {
                return ProjectileSystemBridge.GetProjectileInterface(legacyProjectile.gameObject);
            }
            return null;
        }

        private void SetupProjectileInterface(IProjectile projectile, ProjectileSpawnRequest request)
        {
            try
            {
                projectile.Transform.position = request.Position;
                projectile.Transform.rotation = request.Rotation;
                projectile.Transform.localScale = Vector3.one * request.Scale;

                projectile.SetupProjectile(
                    request.Damage,
                    request.Speed,
                    request.Lifetime,
                    request.EnableHoming,
                    request.Scale,
                    request.Target
                );
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePool] Error setting up projectile interface: {e.Message}");
                ReturnProjectileInterface(projectile);
            }
        }

        /// <summary>
        /// Helper method to get the ProjectileStateBased component from the legacy prefab
        /// </summary>
        private ProjectileStateBased GetLegacyPrefabComponent()
        {
            if (legacyProjectilePrefab != null)
            {
                return legacyProjectilePrefab.GetComponent<ProjectileStateBased>();
            }
            return null;
        }

        /// <summary>
        /// Create a legacy-compatible projectile when only new system prefab is available
        /// </summary>
        private ProjectileStateBased CreateLegacyFromNewSystem()
        {
            if (newProjectilePrefab == null) return null;

            // Check if the new prefab actually has a ProjectileStateBased component (hybrid prefab)
            var legacyComponent = newProjectilePrefab.GetComponent<ProjectileStateBased>();
            if (legacyComponent != null)
            {
                // It's a hybrid prefab with both systems - use the legacy component
                return CreateLegacyProjectile(legacyComponent, true);
            }

            // Pure new system prefab - we can't create a ProjectileStateBased from it
            // Return null and let the caller handle this case
            Debug.LogWarning("[ProjectilePool] Cannot create ProjectileStateBased from pure ProjectileEntity prefab. " +
                           "Use GetProjectileInterface() instead, or assign a legacy prefab.");
            return null;
        }
        #endregion

        public void ClearPool()
        {
            if (projectileContainer != null)
            {
                foreach (Transform child in projectileContainer)
                {
                    Destroy(child.gameObject);
                }
            }

            projectilePool.Clear();
            activeProjectiles.Clear();
            newProjectilePool.Clear();
            activeNewProjectiles.Clear();
            totalProjectilesCreated = 0;
            projectileRequestQueue.Clear();
        }

        public void CheckAndReplenishPool()
        {
            while (projectilePool.Count < initialPoolSize)
            {
                CreateNewProjectile();
            }
        }

        public int GetProjectileRequestCount()
        {
            return projectileRequestQueue.Count;
        }

        public int GetPoolSize()
        {
            return projectilePool.Count;
        }

        public void ClearProjectileRequests()
        {
            projectileRequestQueue.Clear();
        }

        public void InitializeProjectilePool()
        {
            ClearPool();
            InitializePool();
        }

        public bool HasPendingRequests()
        {
            return !projectileRequestQueue.IsEmpty;
        }

        // Public properties for debugging
        public int CurrentPoolSize => projectilePool.Count;
        public int ActiveCount => activeProjectiles.Count;
        public float CurrentUtilization => activeProjectiles.Count / (float)(projectilePool.Count + activeProjectiles.Count);
    }
}
