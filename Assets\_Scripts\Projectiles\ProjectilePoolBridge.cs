using UnityEngine;
using System.Collections.Generic;
using System.Collections.Concurrent;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Bridge class that extends ProjectilePool functionality to work with both 
    /// legacy (ProjectileStateBased) and new (ProjectileEntity) projectile systems.
    /// This allows gradual migration without breaking existing functionality.
    /// </summary>
    public class ProjectilePoolBridge : MonoBehaviour
    {
        [Header("Pool Configuration")]
        [SerializeField] private GameObject legacyProjectilePrefab; // ProjectileStateBased prefab
        [SerializeField] private GameObject newProjectilePrefab;    // ProjectileEntity prefab
        [SerializeField] private bool preferNewSystem = false;      // Which system to use by default
        
        [Header("Pool Settings")]
        [SerializeField] private int initialPoolSize = 50;
        [SerializeField] private int maxPoolSize = 200;
        
        private Queue<IProjectile> availableProjectiles = new Queue<IProjectile>();
        private HashSet<IProjectile> activeProjectiles = new HashSet<IProjectile>();
        private ConcurrentQueue<ProjectileSpawnRequest> spawnRequests = new ConcurrentQueue<ProjectileSpawnRequest>();
        
        private Transform poolContainer;
        private int totalProjectilesCreated = 0;

        public static ProjectilePoolBridge Instance { get; private set; }

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializePool();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            PrewarmPool();
        }

        private void Update()
        {
            ProcessSpawnRequests();
        }
        #endregion

        #region Pool Management
        private void InitializePool()
        {
            // Create container for pooled projectiles
            poolContainer = new GameObject("ProjectilePool_Bridge").transform;
            poolContainer.SetParent(transform);
            
            Debug.Log("[ProjectilePoolBridge] Initialized with dual-system support");
        }

        private void PrewarmPool()
        {
            for (int i = 0; i < initialPoolSize; i++)
            {
                CreatePooledProjectile();
            }
            
            Debug.Log($"[ProjectilePoolBridge] Prewarmed pool with {initialPoolSize} projectiles");
        }

        private IProjectile CreatePooledProjectile()
        {
            GameObject prefabToUse = preferNewSystem && newProjectilePrefab != null ? newProjectilePrefab : legacyProjectilePrefab;
            
            if (prefabToUse == null)
            {
                Debug.LogError("[ProjectilePoolBridge] No projectile prefab assigned!");
                return null;
            }

            GameObject projectileObj = Instantiate(prefabToUse, poolContainer);
            projectileObj.SetActive(false);
            
            // Ensure we can get the IProjectile interface
            IProjectile projectile = ProjectileSystemBridge.GetProjectileInterface(projectileObj);
            if (projectile == null)
            {
                Debug.LogError($"[ProjectilePoolBridge] Failed to get IProjectile interface from {prefabToUse.name}");
                Destroy(projectileObj);
                return null;
            }

            availableProjectiles.Enqueue(projectile);
            totalProjectilesCreated++;
            
            return projectile;
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get a projectile from the pool (unified interface)
        /// </summary>
        public IProjectile GetProjectile()
        {
            if (availableProjectiles.Count == 0 && totalProjectilesCreated < maxPoolSize)
            {
                CreatePooledProjectile();
            }

            if (availableProjectiles.Count > 0)
            {
                var projectile = availableProjectiles.Dequeue();
                activeProjectiles.Add(projectile);
                return projectile;
            }

            Debug.LogWarning("[ProjectilePoolBridge] Pool exhausted and max size reached!");
            return null;
        }

        /// <summary>
        /// Get a projectile and set it up with the provided request
        /// </summary>
        public IProjectile GetProjectile(ProjectileSpawnRequest request)
        {
            var projectile = GetProjectile();
            if (projectile != null)
            {
                SetupProjectile(projectile, request);
            }
            return projectile;
        }

        /// <summary>
        /// Return a projectile to the pool
        /// </summary>
        public void ReturnProjectile(IProjectile projectile)
        {
            if (projectile == null) return;

            if (activeProjectiles.Remove(projectile))
            {
                // Reset projectile state
                projectile.ResetForPool();
                projectile.GameObject.SetActive(false);
                
                // Return to pool
                availableProjectiles.Enqueue(projectile);
                
                Debug.Log($"[ProjectilePoolBridge] Returned {projectile.GetProjectileSystemType()} projectile to pool");
            }
        }

        /// <summary>
        /// Return a projectile to the pool by GameObject
        /// </summary>
        public void ReturnProjectile(GameObject projectileObj)
        {
            var projectile = ProjectileSystemBridge.GetProjectileInterface(projectileObj);
            ReturnProjectile(projectile);
        }

        /// <summary>
        /// Enqueue a spawn request for batch processing
        /// </summary>
        public void EnqueueSpawnRequest(ProjectileSpawnRequest request)
        {
            spawnRequests.Enqueue(request);
        }
        #endregion

        #region Private Methods
        private void SetupProjectile(IProjectile projectile, ProjectileSpawnRequest request)
        {
            if (projectile == null) return;

            try
            {
                // Position and rotation
                projectile.Transform.position = request.Position;
                projectile.Transform.rotation = request.Rotation;
                projectile.Transform.localScale = Vector3.one * request.Scale;

                // Setup projectile properties
                projectile.SetupProjectile(
                    request.Damage,
                    request.Speed,
                    request.Lifetime,
                    request.EnableHoming,
                    request.Scale,
                    request.Target
                );

                // Activate the projectile
                projectile.GameObject.SetActive(true);
                
                Debug.Log($"[ProjectilePoolBridge] Setup {projectile.GetProjectileSystemType()} projectile - Damage: {request.Damage}, Speed: {request.Speed}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePoolBridge] Error setting up projectile: {e.Message}");
                ReturnProjectile(projectile);
            }
        }

        private void ProcessSpawnRequests()
        {
            int processed = 0;
            const int maxPerFrame = 5; // Limit processing to avoid frame drops

            while (spawnRequests.TryDequeue(out ProjectileSpawnRequest request) && processed < maxPerFrame)
            {
                var projectile = GetProjectile(request);
                if (projectile != null)
                {
                    // Register with managers if needed
                    RegisterWithManagers(projectile);
                }
                processed++;
            }
        }

        private void RegisterWithManagers(IProjectile projectile)
        {
            // Register with ProjectileManager if it exists
            if (ProjectileManager.Instance != null)
            {
                // Note: This will need to be updated when ProjectileManager supports IProjectile
                Debug.Log($"[ProjectilePoolBridge] Would register {projectile.GetProjectileSystemType()} projectile with ProjectileManager");
            }

            // Register with tracking manager if it exists
            if (ProjectileTrackingManager.Instance != null)
            {
                // Note: This will need to be updated when ProjectileTrackingManager supports IProjectile
                Debug.Log($"[ProjectilePoolBridge] Would register {projectile.GetProjectileSystemType()} projectile with ProjectileTrackingManager");
            }
        }
        #endregion

        #region Debug and Statistics
        public int GetActiveProjectileCount() => activeProjectiles.Count;
        public int GetAvailableProjectileCount() => availableProjectiles.Count;
        public int GetTotalProjectilesCreated() => totalProjectilesCreated;
        public int GetPendingRequestCount() => spawnRequests.Count;

        [ContextMenu("Debug Pool Status")]
        public void DebugPoolStatus()
        {
            Debug.Log($"[ProjectilePoolBridge] Pool Status:\n" +
                     $"Active: {GetActiveProjectileCount()}\n" +
                     $"Available: {GetAvailableProjectileCount()}\n" +
                     $"Total Created: {GetTotalProjectilesCreated()}\n" +
                     $"Pending Requests: {GetPendingRequestCount()}\n" +
                     $"Prefer New System: {preferNewSystem}");
        }
        #endregion
    }
}
