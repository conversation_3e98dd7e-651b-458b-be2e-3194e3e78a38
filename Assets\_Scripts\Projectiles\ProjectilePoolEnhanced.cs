using UnityEngine;
using System.Collections.Generic;
using System.Collections.Concurrent;
using BTR.Projectiles;

namespace BTR
{
    /// <summary>
    /// Enhanced ProjectilePool that supports both ProjectileStateBased (legacy) and ProjectileEntity (new) systems
    /// through the unified IProjectile interface. This allows gradual migration without breaking existing functionality.
    /// </summary>
    [DefaultExecutionOrder(-300)]
    public class ProjectilePoolEnhanced : MonoBehaviour
    {
        public static ProjectilePoolEnhanced Instance { get; private set; }

        [Header("Projectile Prefabs")]
        [SerializeField] private GameObject legacyProjectilePrefab;  // ProjectileStateBased prefab
        [SerializeField] private GameObject newProjectilePrefab;     // ProjectileEntity prefab
        
        [Header("Pool Settings")]
        [SerializeField] private bool preferNewSystem = false;       // Which system to use by default
        [SerializeField] private int initialPoolSize = 50;
        [SerializeField] private int maxPoolSize = 200;
        
        [Head<PERSON>("Dynamic Pool Settings")]
        [SerializeField] private bool enableDynamicPooling = true;
        [SerializeField] private float growthThreshold = 0.9f;
        [SerializeField] private int growthAmount = 25;
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;

        // Unified pools using IProjectile interface
        private readonly Queue<IProjectile> availableProjectiles = new Queue<IProjectile>();
        private readonly HashSet<IProjectile> activeProjectiles = new HashSet<IProjectile>();
        private readonly ConcurrentQueue<ProjectileSpawnRequest> projectileRequestQueue = new ConcurrentQueue<ProjectileSpawnRequest>();
        
        private Transform projectileContainer;
        private int totalProjectilesCreated = 0;
        private readonly object poolLock = new object();

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            // Validate prefabs
            if (legacyProjectilePrefab == null && newProjectilePrefab == null)
            {
                Debug.LogError("[ProjectilePoolEnhanced] No projectile prefabs assigned! Please assign at least one prefab.");
                enabled = false;
                return;
            }

            // Create container
            projectileContainer = new GameObject("ProjectileContainer_Enhanced").transform;
            projectileContainer.SetParent(transform);

            InitializePool();
        }

        private void Update()
        {
            ProcessProjectileRequests();
            
            if (enableDynamicPooling)
            {
                UpdatePoolSize();
            }
        }

        private void OnDestroy()
        {
            lock (poolLock)
            {
                ClearPool();
                Instance = null;
            }
        }
        #endregion

        #region Pool Management
        private void InitializePool()
        {
            lock (poolLock)
            {
                for (int i = 0; i < initialPoolSize; i++)
                {
                    CreatePooledProjectile();
                }
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectilePoolEnhanced] Initialized pool with {initialPoolSize} projectiles");
                }
            }
        }

        private IProjectile CreatePooledProjectile()
        {
            GameObject prefabToUse = GetPreferredPrefab();
            if (prefabToUse == null) return null;

            GameObject projectileObj = Instantiate(prefabToUse, projectileContainer);
            projectileObj.SetActive(false);

            // Get the IProjectile interface
            IProjectile projectile = ProjectileSystemBridge.GetProjectileInterface(projectileObj);
            if (projectile == null)
            {
                Debug.LogError($"[ProjectilePoolEnhanced] Failed to get IProjectile interface from {prefabToUse.name}");
                Destroy(projectileObj);
                return null;
            }

            availableProjectiles.Enqueue(projectile);
            totalProjectilesCreated++;

            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectilePoolEnhanced] Created {projectile.GetProjectileSystemType()} projectile");
            }

            return projectile;
        }

        private GameObject GetPreferredPrefab()
        {
            if (preferNewSystem && newProjectilePrefab != null)
                return newProjectilePrefab;
            else if (legacyProjectilePrefab != null)
                return legacyProjectilePrefab;
            else if (newProjectilePrefab != null)
                return newProjectilePrefab;
            
            return null;
        }

        private void ClearPool()
        {
            while (availableProjectiles.Count > 0)
            {
                var projectile = availableProjectiles.Dequeue();
                if (projectile?.GameObject != null)
                {
                    Destroy(projectile.GameObject);
                }
            }

            foreach (var projectile in activeProjectiles)
            {
                if (projectile?.GameObject != null)
                {
                    Destroy(projectile.GameObject);
                }
            }

            activeProjectiles.Clear();
            totalProjectilesCreated = 0;
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get a projectile using the unified IProjectile interface
        /// </summary>
        public IProjectile GetProjectile()
        {
            lock (poolLock)
            {
                if (availableProjectiles.Count == 0 && totalProjectilesCreated < maxPoolSize)
                {
                    CreatePooledProjectile();
                }

                if (availableProjectiles.Count > 0)
                {
                    var projectile = availableProjectiles.Dequeue();
                    activeProjectiles.Add(projectile);
                    
                    // Reset and activate
                    projectile.ResetForPool();
                    projectile.GameObject.SetActive(true);
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[ProjectilePoolEnhanced] Retrieved {projectile.GetProjectileSystemType()} projectile");
                    }
                    
                    return projectile;
                }

                Debug.LogWarning("[ProjectilePoolEnhanced] Pool exhausted and max size reached!");
                return null;
            }
        }

        /// <summary>
        /// Get a legacy ProjectileStateBased for compatibility with existing code
        /// </summary>
        public ProjectileStateBased GetLegacyProjectile()
        {
            var projectile = GetProjectile();
            if (projectile != null && projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // Get the underlying ProjectileStateBased from the adapter
                if (projectile is ProjectileStateBased_IProjectileAdapter adapter)
                {
                    return adapter.GetProjectileStateBased();
                }
            }
            
            // If we got a new system projectile but legacy was requested, return it to pool and try to get legacy
            if (projectile != null)
            {
                ReturnProjectile(projectile);
                
                // Force create a legacy projectile if available
                if (legacyProjectilePrefab != null)
                {
                    var legacyObj = Instantiate(legacyProjectilePrefab, projectileContainer);
                    var legacyInterface = ProjectileSystemBridge.GetProjectileInterface(legacyObj);
                    if (legacyInterface is ProjectileStateBased_IProjectileAdapter legacyAdapter)
                    {
                        activeProjectiles.Add(legacyInterface);
                        legacyObj.SetActive(true);
                        return legacyAdapter.GetProjectileStateBased();
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Get a projectile and set it up with the provided request
        /// </summary>
        public IProjectile GetProjectile(ProjectileSpawnRequest request)
        {
            var projectile = GetProjectile();
            if (projectile != null)
            {
                SetupProjectile(projectile, request);
            }
            return projectile;
        }

        /// <summary>
        /// Return a projectile to the pool
        /// </summary>
        public void ReturnProjectile(IProjectile projectile)
        {
            if (projectile == null) return;

            lock (poolLock)
            {
                if (activeProjectiles.Remove(projectile))
                {
                    projectile.ResetForPool();
                    projectile.GameObject.SetActive(false);
                    availableProjectiles.Enqueue(projectile);
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[ProjectilePoolEnhanced] Returned {projectile.GetProjectileSystemType()} projectile to pool");
                    }
                }
            }
        }

        /// <summary>
        /// Return a legacy ProjectileStateBased to the pool
        /// </summary>
        public void ReturnProjectileToPool(ProjectileStateBased legacyProjectile)
        {
            if (legacyProjectile == null) return;

            var projectile = ProjectileSystemBridge.GetProjectileInterface(legacyProjectile.gameObject);
            ReturnProjectile(projectile);
        }

        /// <summary>
        /// Enqueue a spawn request for batch processing
        /// </summary>
        public void EnqueueProjectileRequest(ProjectileSpawnRequest request)
        {
            projectileRequestQueue.Enqueue(request);
        }
        #endregion

        #region Private Methods
        private void SetupProjectile(IProjectile projectile, ProjectileSpawnRequest request)
        {
            try
            {
                projectile.Transform.position = request.Position;
                projectile.Transform.rotation = request.Rotation;
                projectile.Transform.localScale = Vector3.one * request.Scale;

                projectile.SetupProjectile(
                    request.Damage,
                    request.Speed,
                    request.Lifetime,
                    request.EnableHoming,
                    request.Scale,
                    request.Target
                );

                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectilePoolEnhanced] Setup {projectile.GetProjectileSystemType()} projectile");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectilePoolEnhanced] Error setting up projectile: {e.Message}");
                ReturnProjectile(projectile);
            }
        }

        private void ProcessProjectileRequests()
        {
            const int MAX_REQUESTS_PER_FRAME = 5;
            int processedCount = 0;

            while (processedCount < MAX_REQUESTS_PER_FRAME && projectileRequestQueue.TryDequeue(out var request))
            {
                var projectile = GetProjectile(request);
                if (projectile != null)
                {
                    processedCount++;
                }
            }
        }

        private void UpdatePoolSize()
        {
            lock (poolLock)
            {
                float utilization = (float)activeProjectiles.Count / (availableProjectiles.Count + activeProjectiles.Count);
                
                if (utilization > growthThreshold && totalProjectilesCreated < maxPoolSize)
                {
                    int growthSize = Mathf.Min(growthAmount, maxPoolSize - totalProjectilesCreated);
                    for (int i = 0; i < growthSize; i++)
                    {
                        CreatePooledProjectile();
                    }
                }
            }
        }
        #endregion

        #region Public Properties and Debug
        public int GetActiveProjectileCount() => activeProjectiles.Count;
        public int GetAvailableProjectileCount() => availableProjectiles.Count;
        public int GetTotalProjectilesCreated() => totalProjectilesCreated;
        public bool IsUsingNewSystem() => preferNewSystem && newProjectilePrefab != null;

        [ContextMenu("Debug Pool Status")]
        public void DebugPoolStatus()
        {
            Debug.Log($"[ProjectilePoolEnhanced] Pool Status:\n" +
                     $"Active: {GetActiveProjectileCount()}\n" +
                     $"Available: {GetAvailableProjectileCount()}\n" +
                     $"Total Created: {GetTotalProjectilesCreated()}\n" +
                     $"Prefer New System: {preferNewSystem}\n" +
                     $"Legacy Prefab: {(legacyProjectilePrefab != null ? legacyProjectilePrefab.name : "None")}\n" +
                     $"New Prefab: {(newProjectilePrefab != null ? newProjectilePrefab.name : "None")}");
        }
        #endregion
    }
}
