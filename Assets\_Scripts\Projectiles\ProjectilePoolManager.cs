using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Simple manager that allows you to use ProjectileEntity prefabs with the existing ProjectilePool system.
    /// This provides a bridge between the old and new systems without requiring major code changes.
    /// </summary>
    public class ProjectilePoolManager : MonoBehaviour
    {
        [Header("Projectile Prefabs")]
        [SerializeField] private GameObject legacyProjectilePrefab; // ProjectileStateBased prefab
        [SerializeField] private GameObject newProjectilePrefab;    // ProjectileEntity prefab
        
        [Header("Pool Settings")]
        [SerializeField] private bool useNewSystem = true;         // Which system to prefer
        [SerializeField] private int poolSize = 50;
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;

        public static ProjectilePoolManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectilePoolManager] Initialized - Using {(useNewSystem ? "New" : "Legacy")} system");
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Get a projectile using the unified interface
        /// </summary>
        public IProjectile GetProjectileInterface()
        {
            if (useNewSystem && newProjectilePrefab != null)
            {
                return GetNewProjectile();
            }
            else if (legacyProjectilePrefab != null)
            {
                return GetLegacyProjectile();
            }
            else
            {
                Debug.LogError("[ProjectilePoolManager] No projectile prefabs assigned!");
                return null;
            }
        }

        /// <summary>
        /// Get a legacy ProjectileStateBased (for compatibility with existing code)
        /// </summary>
        public ProjectileStateBased GetLegacyProjectileStateBased()
        {
            if (useNewSystem)
            {
                Debug.LogWarning("[ProjectilePoolManager] Requested legacy projectile but new system is preferred. Consider using GetProjectileInterface() instead.");
            }

            // Use the existing ProjectilePool for legacy projectiles
            return ProjectilePool.Instance?.GetProjectile();
        }

        /// <summary>
        /// Setup a projectile with the unified interface
        /// </summary>
        public IProjectile SetupProjectile(float damage, float speed, float lifetime, bool enableHoming, float scale, Transform target)
        {
            var projectile = GetProjectileInterface();
            if (projectile != null)
            {
                projectile.SetupProjectile(damage, speed, lifetime, enableHoming, scale, target);
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectilePoolManager] Setup {projectile.GetProjectileSystemType()} projectile - Damage: {damage}, Speed: {speed}");
                }
            }
            return projectile;
        }

        /// <summary>
        /// Return a projectile to the appropriate pool
        /// </summary>
        public void ReturnProjectile(IProjectile projectile)
        {
            if (projectile == null) return;

            if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // Return to legacy pool
                var legacyProjectile = ProjectileSystemBridge.GetProjectileInterface(projectile.GameObject) as ProjectileStateBased_IProjectileAdapter;
                if (legacyProjectile != null)
                {
                    ProjectilePool.Instance?.ReturnProjectileToPool(legacyProjectile.GetProjectileStateBased());
                }
            }
            else
            {
                // Handle new system return (simple destroy for now, can be improved with pooling later)
                if (projectile.GameObject != null)
                {
                    projectile.GameObject.SetActive(false);
                    Destroy(projectile.GameObject);
                }
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectilePoolManager] Returned {projectile.GetProjectileSystemType()} projectile to pool");
            }
        }

        private IProjectile GetNewProjectile()
        {
            if (newProjectilePrefab == null)
            {
                Debug.LogError("[ProjectilePoolManager] New projectile prefab is not assigned!");
                return null;
            }

            // Instantiate new projectile
            GameObject projectileObj = Instantiate(newProjectilePrefab);
            var projectileEntity = projectileObj.GetComponent<ProjectileEntity>();
            
            if (projectileEntity == null)
            {
                Debug.LogError("[ProjectilePoolManager] New projectile prefab doesn't have ProjectileEntity component!");
                Destroy(projectileObj);
                return null;
            }

            if (enableDebugLogs)
            {
                Debug.Log("[ProjectilePoolManager] Created new ProjectileEntity");
            }

            return projectileEntity;
        }

        private IProjectile GetLegacyProjectile()
        {
            // Use the existing ProjectilePool and wrap with adapter
            var legacyProjectile = ProjectilePool.Instance?.GetProjectile();
            if (legacyProjectile != null)
            {
                var adapter = ProjectileSystemBridge.GetProjectileInterface(legacyProjectile.gameObject);
                
                if (enableDebugLogs)
                {
                    Debug.Log("[ProjectilePoolManager] Got legacy projectile with adapter");
                }
                
                return adapter;
            }

            return null;
        }

        /// <summary>
        /// Switch between legacy and new systems
        /// </summary>
        public void SetUseNewSystem(bool useNew)
        {
            useNewSystem = useNew;
            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectilePoolManager] Switched to {(useNew ? "New" : "Legacy")} system");
            }
        }

        /// <summary>
        /// Check which system is currently being used
        /// </summary>
        public bool IsUsingNewSystem()
        {
            return useNewSystem && newProjectilePrefab != null;
        }

        /// <summary>
        /// Get debug information about the current setup
        /// </summary>
        [ContextMenu("Debug Pool Status")]
        public void DebugPoolStatus()
        {
            Debug.Log($"[ProjectilePoolManager] Status:\n" +
                     $"Use New System: {useNewSystem}\n" +
                     $"Legacy Prefab: {(legacyProjectilePrefab != null ? legacyProjectilePrefab.name : "None")}\n" +
                     $"New Prefab: {(newProjectilePrefab != null ? newProjectilePrefab.name : "None")}\n" +
                     $"Currently Using: {(IsUsingNewSystem() ? "New System" : "Legacy System")}");
        }
    }
}
