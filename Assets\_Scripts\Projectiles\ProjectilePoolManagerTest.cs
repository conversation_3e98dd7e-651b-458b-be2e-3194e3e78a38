using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Simple test script to verify ProjectilePoolManager works with both systems
    /// </summary>
    public class ProjectilePoolManagerTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private KeyCode testKey = KeyCode.T;
        
        [Header("Test Parameters")]
        [SerializeField] private float testDamage = 25f;
        [SerializeField] private float testSpeed = 50f;
        [SerializeField] private float testLifetime = 3f;
        [SerializeField] private bool testHoming = false;
        [SerializeField] private float testScale = 1f;

        private void Start()
        {
            if (runTestOnStart)
            {
                Invoke(nameof(RunPoolManagerTest), 1f); // Wait a second for everything to initialize
            }
        }

        private void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                RunPoolManagerTest();
            }
        }

        [ContextMenu("Run Pool Manager Test")]
        public void RunPoolManagerTest()
        {
            Debug.Log("=== PROJECTILE POOL MANAGER TEST ===");

            if (ProjectilePoolManager.Instance == null)
            {
                Debug.LogError("❌ ProjectilePoolManager.Instance is null! Make sure you have a ProjectilePoolManager in the scene.");
                return;
            }

            // Test 1: Get projectile using unified interface
            TestUnifiedInterface();

            // Test 2: Test system switching
            TestSystemSwitching();

            // Test 3: Test projectile setup
            TestProjectileSetup();

            Debug.Log("=== POOL MANAGER TEST COMPLETED ===");
        }

        private void TestUnifiedInterface()
        {
            Debug.Log("--- Test 1: Unified Interface ---");

            var projectile = ProjectilePoolManager.Instance.GetProjectileInterface();
            if (projectile != null)
            {
                Debug.Log($"✅ Got projectile: {projectile.GetProjectileSystemType()}");
                Debug.Log($"GameObject: {projectile.GameObject.name}");
                Debug.Log($"Transform: {projectile.Transform.name}");
                Debug.Log($"Instance ID: {projectile.InstanceID}");
                
                // Clean up
                ProjectilePoolManager.Instance.ReturnProjectile(projectile);
                Debug.Log("✅ Returned projectile to pool");
            }
            else
            {
                Debug.LogError("❌ Failed to get projectile from unified interface");
            }
        }

        private void TestSystemSwitching()
        {
            Debug.Log("--- Test 2: System Switching ---");

            bool originalSetting = ProjectilePoolManager.Instance.IsUsingNewSystem();
            Debug.Log($"Original system: {(originalSetting ? "New" : "Legacy")}");

            // Switch to opposite system
            ProjectilePoolManager.Instance.SetUseNewSystem(!originalSetting);
            Debug.Log($"Switched to: {(ProjectilePoolManager.Instance.IsUsingNewSystem() ? "New" : "Legacy")}");

            // Test getting projectile with switched system
            var projectile = ProjectilePoolManager.Instance.GetProjectileInterface();
            if (projectile != null)
            {
                Debug.Log($"✅ Got projectile from switched system: {projectile.GetProjectileSystemType()}");
                ProjectilePoolManager.Instance.ReturnProjectile(projectile);
            }

            // Switch back
            ProjectilePoolManager.Instance.SetUseNewSystem(originalSetting);
            Debug.Log($"Switched back to: {(ProjectilePoolManager.Instance.IsUsingNewSystem() ? "New" : "Legacy")}");
        }

        private void TestProjectileSetup()
        {
            Debug.Log("--- Test 3: Projectile Setup ---");

            // Position projectile in front of camera
            Vector3 spawnPosition = Camera.main != null ? 
                Camera.main.transform.position + Camera.main.transform.forward * 5f : 
                Vector3.forward * 5f;

            var projectile = ProjectilePoolManager.Instance.SetupProjectile(
                testDamage, testSpeed, testLifetime, testHoming, testScale, null);

            if (projectile != null)
            {
                // Position the projectile
                projectile.Transform.position = spawnPosition;
                projectile.Transform.rotation = Quaternion.LookRotation(Vector3.forward);
                
                // Activate it
                projectile.GameObject.SetActive(true);

                Debug.Log($"✅ Setup and spawned {projectile.GetProjectileSystemType()} projectile");
                Debug.Log($"Position: {projectile.Transform.position}");
                Debug.Log($"Damage: {projectile.DamageAmount}");
                Debug.Log($"Speed: {projectile.BulletSpeed}");
                Debug.Log($"Lifetime: {projectile.Lifetime}");

                // Let it live for a bit, then clean up
                StartCoroutine(CleanupProjectileAfterDelay(projectile, 2f));
            }
            else
            {
                Debug.LogError("❌ Failed to setup projectile");
            }
        }

        private System.Collections.IEnumerator CleanupProjectileAfterDelay(IProjectile projectile, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (projectile != null && projectile.GameObject != null)
            {
                Debug.Log($"Cleaning up test projectile: {projectile.GetProjectileSystemType()}");
                ProjectilePoolManager.Instance.ReturnProjectile(projectile);
            }
        }
    }
}
