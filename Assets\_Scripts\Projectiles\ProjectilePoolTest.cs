using UnityEngine;
using BTR.Projectiles;

namespace BTR
{
    /// <summary>
    /// Simple test script to verify the enhanced ProjectilePool works with both systems
    /// </summary>
    public class ProjectilePoolTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private KeyCode testKey = KeyCode.T;
        
        [Header("Test Parameters")]
        [SerializeField] private float testDamage = 25f;
        [SerializeField] private float testSpeed = 50f;
        [SerializeField] private float testLifetime = 3f;
        [SerializeField] private bool testHoming = false;
        [SerializeField] private float testScale = 1f;

        private void Start()
        {
            if (runTestOnStart)
            {
                Invoke(nameof(RunProjectilePoolTest), 1f); // Wait a second for everything to initialize
            }
        }

        private void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                RunProjectilePoolTest();
            }
        }

        [ContextMenu("Run Projectile Pool Test")]
        public void RunProjectilePoolTest()
        {
            Debug.Log("=== ENHANCED PROJECTILE POOL TEST ===");

            if (ProjectilePool.Instance == null)
            {
                Debug.LogError("❌ ProjectilePool.Instance is null! Make sure you have a ProjectilePool in the scene.");
                return;
            }

            // Test 1: Get projectile using new unified interface
            TestUnifiedInterface();

            // Test 2: Test legacy compatibility
            TestLegacyCompatibility();

            // Test 3: Test projectile setup with spawn request
            TestSpawnRequest();

            Debug.Log("=== PROJECTILE POOL TEST COMPLETED ===");
        }

        private void TestUnifiedInterface()
        {
            Debug.Log("--- Test 1: Unified Interface ---");

            var projectile = ProjectilePool.Instance.GetProjectileInterface();
            if (projectile != null)
            {
                Debug.Log($"✅ Got projectile: {projectile.GetProjectileSystemType()}");
                Debug.Log($"GameObject: {projectile.GameObject.name}");
                Debug.Log($"Transform: {projectile.Transform.name}");
                Debug.Log($"Instance ID: {projectile.InstanceID}");
                
                // Clean up
                ProjectilePool.Instance.ReturnProjectileInterface(projectile);
                Debug.Log("✅ Returned projectile to pool");
            }
            else
            {
                Debug.LogError("❌ Failed to get projectile from unified interface");
            }
        }

        private void TestLegacyCompatibility()
        {
            Debug.Log("--- Test 2: Legacy Compatibility ---");

            var legacyProjectile = ProjectilePool.Instance.GetProjectile();
            if (legacyProjectile != null)
            {
                Debug.Log($"✅ Got legacy projectile: {legacyProjectile.name}");
                Debug.Log($"Damage: {legacyProjectile.damageAmount}");
                Debug.Log($"Speed: {legacyProjectile.bulletSpeed}");
                
                // Clean up
                ProjectilePool.Instance.ReturnProjectileToPool(legacyProjectile);
                Debug.Log("✅ Returned legacy projectile to pool");
            }
            else
            {
                Debug.LogError("❌ Failed to get legacy projectile");
            }
        }

        private void TestSpawnRequest()
        {
            Debug.Log("--- Test 3: Spawn Request ---");

            // Position projectile in front of camera
            Vector3 spawnPosition = Camera.main != null ? 
                Camera.main.transform.position + Camera.main.transform.forward * 5f : 
                Vector3.forward * 5f;

            var request = new ProjectileSpawnRequest
            {
                Position = spawnPosition,
                Rotation = Quaternion.LookRotation(Vector3.forward),
                Speed = testSpeed,
                Lifetime = testLifetime,
                Scale = testScale,
                Damage = testDamage,
                EnableHoming = testHoming,
                Target = null
            };

            var projectile = ProjectilePool.Instance.GetProjectileInterface(request);
            if (projectile != null)
            {
                Debug.Log($"✅ Setup and spawned {projectile.GetProjectileSystemType()} projectile");
                Debug.Log($"Position: {projectile.Transform.position}");
                Debug.Log($"Damage: {projectile.DamageAmount}");
                Debug.Log($"Speed: {projectile.BulletSpeed}");
                Debug.Log($"Lifetime: {projectile.Lifetime}");

                // Let it live for a bit, then clean up
                StartCoroutine(CleanupProjectileAfterDelay(projectile, 2f));
            }
            else
            {
                Debug.LogError("❌ Failed to setup projectile with spawn request");
            }
        }

        private System.Collections.IEnumerator CleanupProjectileAfterDelay(IProjectile projectile, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (projectile != null && projectile.GameObject != null)
            {
                Debug.Log($"Cleaning up test projectile: {projectile.GetProjectileSystemType()}");
                ProjectilePool.Instance.ReturnProjectileInterface(projectile);
            }
        }
    }
}
