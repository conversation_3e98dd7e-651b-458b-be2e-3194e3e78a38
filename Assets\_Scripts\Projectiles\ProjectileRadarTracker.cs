using UnityEngine;
using BTR;
using BTR.Projectiles;

namespace BTR
{
    /// <summary>
    /// Simple component that marks a projectile for radar tracking and provides configuration.
    /// All actual tracking logic is handled by ProjectileTrackingManager.
    /// Works with both legacy ProjectileStateBased and new ProjectileEntity systems.
    /// </summary>
    public class ProjectileRadarTracker : MonoBehaviour
    {
        [Header("Radar Settings")]
        [SerializeField] public int radarIconIndex = 0;  // Made public for ProjectileTrackingManager access

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogging = false;

        private void Awake()
        {
            // Validate that this is attached to a valid projectile
            var legacyProjectile = GetComponent<ProjectileStateBased>();
            var newProjectile = GetComponent<ProjectileEntity>();

            if (legacyProjectile == null && newProjectile == null)
            {
                Debug.LogError($"[ProjectileRadarTracker] Component attached to {gameObject.name} but no ProjectileStateBased or ProjectileEntity found!");
            }
            else if (enableDebugLogging)
            {
                string projectileType = legacyProjectile != null ? "Legacy" : "New";
                Debug.Log($"[ProjectileRadarTracker] Initialized on {projectileType} projectile: {gameObject.name}");
            }
        }
    }
}