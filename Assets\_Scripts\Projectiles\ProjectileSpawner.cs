using System;
using System.Collections;
using System.Collections.Generic;
using Chronos;
using UnityEngine;
using BTR;
using BTR.Projectiles;

namespace BTR
{

    public class ProjectileSpawner : MonoBehaviour
    {
        private static readonly int ColorProperty = Shader.PropertyToID("_Color");
        private static readonly int OpacityProperty = Shader.PropertyToID("_Opacity");
        private static readonly int TimeOffsetProperty = Shader.PropertyToID("_TimeOffset");

        // Singleton instance
        public static ProjectileSpawner Instance { get; private set; }

        private Dictionary<int, Material> materialLookup = new Dictionary<int, Material>();

        private ProjectileStateBased lastCreatedProjectile;

        private ProjectilePool projectilePool;
        private ProjectileEffectManager effectManager;
        private ProjectileManager projectileManager;

        private GlobalClock globalClock;

        public bool IsFullyInitialized =>
            projectilePool != null &&
            effectManager != null &&
            projectileManager != null &&
            globalClock != null;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            projectilePool = GetComponent<ProjectilePool>();
            effectManager = GetComponent<ProjectileEffectManager>();
            projectileManager = GetComponent<ProjectileManager>();

            StartCoroutine(InitializeGlobalClock());
        }

        private IEnumerator InitializeGlobalClock()
        {
            // Wait a few frames to ensure Timekeeper is initialized
            yield return new WaitForSeconds(0.1f);

            if (Timekeeper.instance != null)
            {
                try
                {
                    globalClock = Timekeeper.instance.Clock("Test");
                }
                catch (ChronosException)
                {
                    Debug.LogWarning("Global clock 'Test' not found. Will retry in Start.");
                }
            }
        }

        private void Start()
        {
            // If clock wasn't initialized in Awake, try again
            if (globalClock == null && Timekeeper.instance != null)
            {
                try
                {
                    globalClock = Timekeeper.instance.Clock("Test");
                }
                catch (ChronosException)
                {
                    Debug.LogWarning("Global clock 'Test' still not found in Start. Some functionality may be limited.");
                }
            }
        }

        public void ProcessShootProjectile(ProjectileSpawnRequest request, ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            try
            {
                projectile.transform.position = request.Position;
                projectile.transform.rotation = request.Rotation;
                projectile.transform.localScale = Vector3.one * request.Scale;
                projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, request.Target);
                projectile.gameObject.SetActive(true);
                // NOTE: targetLocking was previously set here. If additional targeting attributes are needed, set them here.

                if (projectile.rb != null)
                {
                    projectile.rb.isKinematic = false;
                    projectile.rb.linearVelocity = request.Rotation * Vector3.forward * request.Speed;
                }

                // Set accuracy
                projectile.SetAccuracy(request.Accuracy);

                // Set clock key if provided
                if (!string.IsNullOrEmpty(request.ClockKey))
                {
                    projectile.SetClock(request.ClockKey);
                }

                ProjectileManager.Instance.RegisterProjectile(projectile);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileSpawner] Error processing projectile request: {e.Message}");
            }
        }

        public bool RequestEnemyShot(Action shotAction)
        {
            float timeScale = globalClock.timeScale;

            if (timeScale <= 0)
            {
                Debug.Log($"[{GetType().Name}] Shot denied due to timeScale <= 0");
                return false;
            }

            shotAction.Invoke();
            return true;
        }

        public void ShootProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float uniformScale,
            float damage,
            bool enableHoming = false,
            Material material = null,
            Transform target = null
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = uniformScale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material)
            };
            ProjectilePool.Instance.EnqueueProjectileRequest(request);

            Debug.Log($"[{GetType().Name}] Enqueued projectile request with scale: {uniformScale}");
        }

        public IProjectile ShootProjectileFromEnemy(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material,
            string clockKey,
            float accuracy,
            Transform target
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material),
                ClockKey = clockKey,
                Accuracy = accuracy
            };

            IProjectile projectile = projectilePool.GetProjectileInterface();
            if (projectile == null)
            {
                Debug.LogError($"[{GetType().Name}] Failed to get projectile for enemy shot!");
                return null;
            }

            ProcessShootProjectileInterface(request, projectile);
            ProjectileManager.Instance.RegisterProjectileInterface(projectile);

            // Handle state management based on projectile type
            SetupEnemyProjectileState(projectile, target);

            Debug.Log($"[{GetType().Name}] Projectile created and registered for enemy. Position: {position}, Speed: {speed}, Target: {(target != null ? target.name : "None")}");

            return projectile;
        }

        private int RegisterMaterial(Material material)
        {
            if (material == null)
                return -1;
            int id = material.GetInstanceID();
            if (!materialLookup.ContainsKey(id))
            {
                materialLookup[id] = material;
            }
            return id;
        }

        private Material GetMaterialById(int materialId)
        {
            if (materialId != -1 && materialLookup.TryGetValue(materialId, out Material material))
            {
                return material;
            }
            return null;
        }

        public ProjectileStateBased GetLastCreatedProjectile()
        {
            return lastCreatedProjectile;
        }

        /// <summary>
        /// Process projectile setup using the unified IProjectile interface
        /// </summary>
        public void ProcessShootProjectileInterface(ProjectileSpawnRequest request, IProjectile projectile)
        {
            if (projectile == null) return;

            try
            {
                Debug.Log($"[ProjectileSpawner] Setting projectile position to: {request.Position}, rotation: {request.Rotation}");

                projectile.Transform.position = request.Position;
                projectile.Transform.rotation = request.Rotation;
                projectile.Transform.localScale = Vector3.one * request.Scale;

                projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, request.Target);
                projectile.GameObject.SetActive(true);

                Debug.Log($"[ProjectileSpawner] Projectile activated at final position: {projectile.Transform.position}");

                // Set up physics if available
                var rb = projectile.GameObject.GetComponent<Rigidbody>();
                if (rb != null)
                {
                    rb.isKinematic = false;
                    rb.linearVelocity = request.Rotation * Vector3.forward * request.Speed;
                }

                // Set accuracy if supported
                if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
                {
                    // For legacy projectiles, try to set accuracy through adapter
                    var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                    adapter?.GetProjectileStateBased()?.SetAccuracy(request.Accuracy);
                }

                // Set clock key if provided and supported
                if (!string.IsNullOrEmpty(request.ClockKey))
                {
                    if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
                    {
                        var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                        adapter?.GetProjectileStateBased()?.SetClock(request.ClockKey);
                    }
                    // For new system, clock management would be handled differently
                }

                Debug.Log($"[ProjectileSpawner] Setup {projectile.GetProjectileSystemType()} projectile with unified interface");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileSpawner] Error processing projectile interface request: {e.Message}");
            }
        }

        /// <summary>
        /// Setup enemy projectile state based on projectile type
        /// </summary>
        private void SetupEnemyProjectileState(IProjectile projectile, Transform target)
        {
            if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // For legacy projectiles, use the traditional state system
                var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                var legacyProjectile = adapter?.GetProjectileStateBased();
                if (legacyProjectile != null)
                {
                    legacyProjectile.ChangeState(new EnemyShotState(legacyProjectile, target));
                }
            }
            else
            {
                // For new system projectiles, handle state differently
                // This would be implemented based on your new state management approach
                Debug.Log($"[ProjectileSpawner] New system projectile state setup - target: {(target != null ? target.name : "None")}");
            }
        }

        /// <summary>
        /// Helper method to get a ProjectileStateBased with fallback to unified interface
        /// </summary>
        private ProjectileStateBased GetProjectileForEnemy()
        {
            // Try legacy method first
            ProjectileStateBased projectile = projectilePool.GetProjectile();
            if (projectile != null)
            {
                return projectile;
            }

            // Fallback: try to get from unified interface and extract ProjectileStateBased
            var projectileInterface = projectilePool.GetProjectileInterface();
            if (projectileInterface != null)
            {
                // If it's a legacy adapter, get the underlying ProjectileStateBased
                if (projectileInterface is ProjectileStateBased_IProjectileAdapter adapter)
                {
                    return adapter.GetProjectileStateBased();
                }

                // If it's a pure new system projectile, create a temporary wrapper
                if (projectileInterface is ProjectileEntity entity)
                {
                    return CreateLegacyWrapperForNewProjectile(entity);
                }

                // Unknown projectile type
                Debug.LogError("[ProjectileSpawner] Unknown projectile type returned from unified interface.");
                projectilePool.ReturnProjectileInterface(projectileInterface);
            }

            return null;
        }

        /// <summary>
        /// Create a temporary ProjectileStateBased wrapper for a ProjectileEntity
        /// This allows legacy code to work with new projectiles
        /// </summary>
        private ProjectileStateBased CreateLegacyWrapperForNewProjectile(ProjectileEntity entity)
        {
            // Add a ProjectileStateBased component to the same GameObject
            var wrapper = entity.gameObject.GetComponent<ProjectileStateBased>();
            if (wrapper == null)
            {
                wrapper = entity.gameObject.AddComponent<ProjectileStateBased>();

                // Initialize the wrapper to delegate to the entity
                InitializeLegacyWrapper(wrapper, entity);
            }

            return wrapper;
        }

        /// <summary>
        /// Initialize the legacy wrapper to work with the new entity
        /// </summary>
        private void InitializeLegacyWrapper(ProjectileStateBased wrapper, ProjectileEntity entity)
        {
            // Copy essential properties from entity to wrapper
            wrapper.damageAmount = entity.DamageAmount;
            wrapper.bulletSpeed = entity.BulletSpeed;
            wrapper.lifetime = entity.Lifetime;
            wrapper.homing = entity.Homing;

            // Set up references
            wrapper.rb = entity.gameObject.GetComponent<Rigidbody>();
            wrapper.modelRenderer = entity.gameObject.GetComponent<Renderer>();

            // Initialize the wrapper's state system if needed
            wrapper.Initialize();
        }

        private Queue<StaticEnemyProjectileRequest> staticEnemyProjectileQueue = new Queue<StaticEnemyProjectileRequest>();
        private bool isProcessingStaticEnemyProjectiles = false;

        private struct StaticEnemyProjectileRequest
        {
            public Vector3 Position;
            public Quaternion Rotation;
            public float Speed;
            public float Lifetime;
            public float Scale;
            public float Damage;
            public bool EnableHoming;
            public int MaterialId; // Change from Material to MaterialId
        }

        public void ShootStaticEnemyProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material)
        {
            staticEnemyProjectileQueue.Enqueue(new StaticEnemyProjectileRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                MaterialId = RegisterMaterial(material)
            });

            if (!isProcessingStaticEnemyProjectiles)
            {
                StartCoroutine(ProcessStaticEnemyProjectiles());
            }
        }

        private IEnumerator ProcessStaticEnemyProjectiles()
        {
            isProcessingStaticEnemyProjectiles = true;
            WaitForSeconds shortDelay = new WaitForSeconds(0.02f); // Reduced delay between batches

            while (staticEnemyProjectileQueue.Count > 0)
            {
                int batchSize = Mathf.Min(30, staticEnemyProjectileQueue.Count); // Process up to 30 projectiles per batch
                for (int i = 0; i < batchSize; i++)
                {
                    if (staticEnemyProjectileQueue.Count > 0)
                    {
                        StaticEnemyProjectileRequest request = staticEnemyProjectileQueue.Dequeue();
                        ProjectileStateBased projectile = GetProjectileForEnemy();

                        if (projectile != null)
                        {
                            SetupStaticEnemyProjectile(projectile, request);
                            projectile.gameObject.SetActive(true);
                            Debug.Log($"[{GetType().Name}] Static enemy projectile shot from {request.Position}");
                        }
                        else
                        {
                            Debug.LogWarning($"[{GetType().Name}] Failed to get projectile from pool for static enemy.");
                        }

                        // Add a small random delay between individual shots in the batch
                        yield return new WaitForSeconds(UnityEngine.Random.Range(0.005f, 0.015f));
                    }
                }

                yield return shortDelay; // Short delay between batches
            }

            isProcessingStaticEnemyProjectiles = false;
        }

        private void SetupStaticEnemyProjectile(ProjectileStateBased projectile, StaticEnemyProjectileRequest request)
        {
            projectile.transform.position = request.Position;
            projectile.transform.rotation = request.Rotation;
            projectile.transform.localScale = Vector3.one * request.Scale;
            projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, null);

            if (request.MaterialId != -1 && projectile.modelRenderer != null)
            {
                projectile.modelRenderer.material = GetMaterialById(request.MaterialId);
            }

            // Set the projectile state to EnemyShotState
            projectile.ChangeState(new EnemyShotState(projectile, null));
        }

        public ProjectileStateBased ShootPlayerProjectile(float damage, float speed, float scale)
        {
            if (CrosshairCore.Instance == null)
            {
                Debug.LogError($"[{GetType().Name}] CrosshairCore.Instance is null!");
                return null;
            }

            Vector3 shootPosition = CrosshairCore.Instance.RaySpawn.transform.position;
            Quaternion shootRotation = CrosshairCore.Instance.RaySpawn.transform.rotation;

            Debug.Log($"[{GetType().Name}] ShootPlayerProjectile called. Position: {shootPosition}, Rotation: {shootRotation}");

            ProjectileStateBased projectile = GetProjectileForEnemy();

            if (projectile != null)
            {
                projectile.transform.position = shootPosition;
                projectile.transform.rotation = shootRotation;
                projectile.transform.localScale = Vector3.one * scale;
                projectile.SetupProjectile(damage, speed, 10f, false, scale, null);
                projectile.gameObject.SetActive(true);

                if (projectile.rb != null)
                {
                    projectile.rb.isKinematic = false;
                    projectile.rb.linearVelocity = shootRotation * Vector3.forward * speed;
                }

                ProjectileManager.Instance.RegisterProjectile(projectile);

                // Set the projectile state to PlayerShotState
                projectile.ChangeState(new PlayerShotState(projectile, 1f, null, false));

                Debug.Log($"[{GetType().Name}] Player projectile created and shot. Position: {shootPosition}, Speed: {speed}, Velocity: {projectile.rb?.linearVelocity}");
            }
            else
            {
                Debug.LogError($"[{GetType().Name}] Failed to get projectile from pool for player shot.");
            }

            return projectile;
        }

        public ProjectileStateBased SetupProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material,
            string clockKey,
            float accuracy,
            Transform target
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material),
                ClockKey = clockKey,
                Accuracy = accuracy
            };

            ProjectileStateBased projectile = GetProjectileForEnemy();
            ProcessShootProjectile(request, projectile);
            ProjectileManager.Instance.RegisterProjectile(projectile);

            Debug.Log($"[{GetType().Name}] Projectile created and registered. Position: {position}, Speed: {speed}, Target: {(target != null ? target.name : "None")}");

            return projectile;
        }
    }
}