using UnityEngine;
using BTR.Projectiles;

namespace BTR
{
    /// <summary>
    /// Adapter component that makes ProjectileStateBased implement the IProjectile interface.
    /// This allows the management systems to work with both legacy and new projectile systems.
    /// This component should be automatically added to ProjectileStateBased prefabs during the transition.
    /// </summary>
    [RequireComponent(typeof(ProjectileStateBased))]
    public class ProjectileStateBased_IProjectileAdapter : MonoBehaviour, IProjectile
    {
        private ProjectileStateBased projectileStateBased;

        #region Unity Lifecycle
        private void Awake()
        {
            projectileStateBased = GetComponent<ProjectileStateBased>();
            if (projectileStateBased == null)
            {
                Debug.LogError("[ProjectileStateBased_IProjectileAdapter] No ProjectileStateBased component found!");
                Destroy(this);
            }
        }
        #endregion

        #region IProjectile Implementation
        public GameObject GameObject => gameObject;
        public Transform Transform => transform;
        public int InstanceID => gameObject.GetInstanceID();
        public bool IsActive => gameObject.activeInHierarchy;

        public float BulletSpeed 
        { 
            get => projectileStateBased?.bulletSpeed ?? 0f;
            set { if (projectileStateBased != null) projectileStateBased.bulletSpeed = value; }
        }

        public bool Homing 
        { 
            get => projectileStateBased?.homing ?? false;
            set { if (projectileStateBased != null) projectileStateBased.homing = value; }
        }

        public Transform CurrentTarget 
        { 
            get => projectileStateBased?.currentTarget;
            set { if (projectileStateBased != null) projectileStateBased.currentTarget = value; }
        }

        public int ProjectileIndex 
        { 
            get => projectileStateBased?.projectileIndex ?? -1;
            set { if (projectileStateBased != null) projectileStateBased.projectileIndex = value; }
        }

        public float DamageAmount 
        { 
            get => projectileStateBased?.damageAmount ?? 0f;
            set { if (projectileStateBased != null) projectileStateBased.damageAmount = value; }
        }

        public bool IsPlayerShot 
        { 
            get => projectileStateBased?.isPlayerShot ?? false;
            set { if (projectileStateBased != null) projectileStateBased.isPlayerShot = value; }
        }

        public float Lifetime 
        { 
            get => projectileStateBased?.lifetime ?? 0f;
            set { if (projectileStateBased != null) projectileStateBased.lifetime = value; }
        }

        public bool HasHitTarget 
        { 
            get => projectileStateBased?.hasHitTarget ?? false;
            set { if (projectileStateBased != null) projectileStateBased.hasHitTarget = value; }
        }

        public Rigidbody Rigidbody => projectileStateBased?.rb;
        public Chronos.Timeline Timeline => projectileStateBased?.TLine;

        public void SetupProjectile(float damage, float speed, float lifetime, bool enableHoming, float scale, Transform target)
        {
            projectileStateBased?.SetupProjectile(damage, speed, lifetime, enableHoming, scale, target);
        }

        public void EnableHoming(bool enable)
        {
            projectileStateBased?.EnableHoming(enable);
        }

        public void Death(bool hitTarget = false)
        {
            projectileStateBased?.Death(hitTarget);
        }

        public void ResetForPool()
        {
            projectileStateBased?.ResetForPool();
        }

        public void PlaySound()
        {
            projectileStateBased?.PlaySound();
        }

        public ProjectileSystemType GetProjectileSystemType()
        {
            return ProjectileSystemType.Legacy;
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Get the underlying ProjectileStateBased component
        /// </summary>
        public ProjectileStateBased GetProjectileStateBased()
        {
            return projectileStateBased;
        }

        /// <summary>
        /// Check if the adapter is properly initialized
        /// </summary>
        public bool IsInitialized => projectileStateBased != null;
        #endregion
    }
}
