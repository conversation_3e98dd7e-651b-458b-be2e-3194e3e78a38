using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Bridge utility class that provides unified access to both legacy (ProjectileStateBased) 
    /// and new (ProjectileEntity) projectile systems during the transition period.
    /// </summary>
    public static class ProjectileSystemBridge
    {
        /// <summary>
        /// Get the IProjectile interface from a GameObject, regardless of which system it uses
        /// </summary>
        public static IProjectile GetProjectileInterface(GameObject gameObject)
        {
            if (gameObject == null) return null;

            // Try new system first
            var projectileEntity = gameObject.GetComponent<ProjectileEntity>();
            if (projectileEntity != null)
            {
                return projectileEntity;
            }

            // Try legacy system with adapter
            var adapter = gameObject.GetComponent<ProjectileStateBased_IProjectileAdapter>();
            if (adapter != null)
            {
                return adapter;
            }

            // Try to create adapter for legacy system
            var legacyProjectile = gameObject.GetComponent<ProjectileStateBased>();
            if (legacyProjectile != null)
            {
                adapter = gameObject.AddComponent<ProjectileStateBased_IProjectileAdapter>();
                return adapter;
            }

            return null;
        }

        /// <summary>
        /// Get the IProjectile interface from a Component, regardless of which system it uses
        /// </summary>
        public static IProjectile GetProjectileInterface(Component component)
        {
            return component != null ? GetProjectileInterface(component.gameObject) : null;
        }

        /// <summary>
        /// Check if a GameObject has any projectile system (legacy or new)
        /// </summary>
        public static bool HasProjectileSystem(GameObject gameObject)
        {
            if (gameObject == null) return false;

            return gameObject.GetComponent<ProjectileEntity>() != null ||
                   gameObject.GetComponent<ProjectileStateBased>() != null;
        }

        /// <summary>
        /// Get the projectile system type for a GameObject
        /// </summary>
        public static ProjectileSystemType? GetProjectileSystemType(GameObject gameObject)
        {
            var projectile = GetProjectileInterface(gameObject);
            return projectile?.GetProjectileSystemType();
        }

        /// <summary>
        /// Setup a projectile using the unified interface
        /// </summary>
        public static void SetupProjectile(GameObject gameObject, float damage, float speed, float lifetime, bool enableHoming, float scale, Transform target)
        {
            var projectile = GetProjectileInterface(gameObject);
            projectile?.SetupProjectile(damage, speed, lifetime, enableHoming, scale, target);
        }

        /// <summary>
        /// Enable/disable homing for a projectile using the unified interface
        /// </summary>
        public static void EnableHoming(GameObject gameObject, bool enable)
        {
            var projectile = GetProjectileInterface(gameObject);
            projectile?.EnableHoming(enable);
        }

        /// <summary>
        /// Destroy a projectile using the unified interface
        /// </summary>
        public static void DestroyProjectile(GameObject gameObject, bool hitTarget = false)
        {
            var projectile = GetProjectileInterface(gameObject);
            projectile?.Death(hitTarget);
        }

        /// <summary>
        /// Reset a projectile for pooling using the unified interface
        /// </summary>
        public static void ResetProjectileForPool(GameObject gameObject)
        {
            var projectile = GetProjectileInterface(gameObject);
            projectile?.ResetForPool();
        }

        /// <summary>
        /// Get projectile properties in a unified way
        /// </summary>
        public static ProjectileProperties GetProjectileProperties(GameObject gameObject)
        {
            var projectile = GetProjectileInterface(gameObject);
            if (projectile == null) return null;

            return new ProjectileProperties
            {
                BulletSpeed = projectile.BulletSpeed,
                DamageAmount = projectile.DamageAmount,
                Lifetime = projectile.Lifetime,
                Homing = projectile.Homing,
                IsPlayerShot = projectile.IsPlayerShot,
                HasHitTarget = projectile.HasHitTarget,
                CurrentTarget = projectile.CurrentTarget,
                ProjectileIndex = projectile.ProjectileIndex,
                SystemType = projectile.GetProjectileSystemType()
            };
        }

        /// <summary>
        /// Set projectile properties in a unified way
        /// </summary>
        public static void SetProjectileProperties(GameObject gameObject, ProjectileProperties properties)
        {
            var projectile = GetProjectileInterface(gameObject);
            if (projectile == null || properties == null) return;

            projectile.BulletSpeed = properties.BulletSpeed;
            projectile.DamageAmount = properties.DamageAmount;
            projectile.Lifetime = properties.Lifetime;
            projectile.Homing = properties.Homing;
            projectile.IsPlayerShot = properties.IsPlayerShot;
            projectile.HasHitTarget = properties.HasHitTarget;
            projectile.CurrentTarget = properties.CurrentTarget;
            projectile.ProjectileIndex = properties.ProjectileIndex;
        }

        /// <summary>
        /// Ensure a legacy projectile has the adapter component
        /// </summary>
        public static void EnsureLegacyAdapter(GameObject gameObject)
        {
            if (gameObject == null) return;

            var legacyProjectile = gameObject.GetComponent<ProjectileStateBased>();
            if (legacyProjectile != null)
            {
                var adapter = gameObject.GetComponent<ProjectileStateBased_IProjectileAdapter>();
                if (adapter == null)
                {
                    gameObject.AddComponent<ProjectileStateBased_IProjectileAdapter>();
                    Debug.Log($"[ProjectileSystemBridge] Added adapter to legacy projectile: {gameObject.name}");
                }
            }
        }
    }

    /// <summary>
    /// Data structure for unified projectile properties
    /// </summary>
    public class ProjectileProperties
    {
        public float BulletSpeed;
        public float DamageAmount;
        public float Lifetime;
        public bool Homing;
        public bool IsPlayerShot;
        public bool HasHitTarget;
        public Transform CurrentTarget;
        public int ProjectileIndex;
        public ProjectileSystemType SystemType;
    }
}
