using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Test script to validate the ProjectileSystemBridge works correctly with both
    /// legacy (ProjectileStateBased) and new (ProjectileEntity) projectile systems.
    /// </summary>
    public class ProjectileSystemBridgeTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private GameObject legacyProjectilePrefab; // Should have ProjectileStateBased
        [SerializeField] private GameObject newProjectilePrefab;    // Should have ProjectileEntity

        [Header("Test Parameters")]
        [SerializeField] private float testDamage = 25f;
        [SerializeField] private float testSpeed = 50f;
        [SerializeField] private float testLifetime = 3f;
        [SerializeField] private bool testHoming = true;
        [SerializeField] private float testScale = 1.5f;

        private void Start()
        {
            if (runTestOnStart)
            {
                RunBridgeSystemTest();
            }
        }

        [ContextMenu("Run Bridge System Test")]
        public void RunBridgeSystemTest()
        {
            Debug.Log("=== PROJECTILE SYSTEM BRIDGE TEST ===");

            // Test 1: Bridge Detection
            TestBridgeDetection();

            // Test 2: Legacy System Integration
            TestLegacySystemIntegration();

            // Test 3: New System Integration
            TestNewSystemIntegration();

            // Test 4: Unified Interface
            TestUnifiedInterface();

            // Test 5: Pool Bridge (if available)
            TestPoolBridge();

            Debug.Log("=== BRIDGE SYSTEM TEST COMPLETED ===");
        }

        private void TestBridgeDetection()
        {
            Debug.Log("--- Test 1: Bridge Detection ---");

            // Test with new system
            if (newProjectilePrefab != null)
            {
                var newObj = Instantiate(newProjectilePrefab);
                var newProjectile = ProjectileSystemBridge.GetProjectileInterface(newObj);
                bool hasNewSystem = ProjectileSystemBridge.HasProjectileSystem(newObj);
                var newSystemType = ProjectileSystemBridge.GetProjectileSystemType(newObj);

                Debug.Log($"New System - Interface: {(newProjectile != null ? "✅" : "❌")}, " +
                         $"Has System: {(hasNewSystem ? "✅" : "❌")}, " +
                         $"Type: {newSystemType}");

                Destroy(newObj);
            }
            else
            {
                Debug.LogWarning("No new projectile prefab assigned for testing");
            }

            // Test with legacy system
            if (legacyProjectilePrefab != null)
            {
                var legacyObj = Instantiate(legacyProjectilePrefab);
                var legacyProjectile = ProjectileSystemBridge.GetProjectileInterface(legacyObj);
                bool hasLegacySystem = ProjectileSystemBridge.HasProjectileSystem(legacyObj);
                var legacySystemType = ProjectileSystemBridge.GetProjectileSystemType(legacyObj);

                Debug.Log($"Legacy System - Interface: {(legacyProjectile != null ? "✅" : "❌")}, " +
                         $"Has System: {(hasLegacySystem ? "✅" : "❌")}, " +
                         $"Type: {legacySystemType}");

                Destroy(legacyObj);
            }
            else
            {
                Debug.LogWarning("No legacy projectile prefab assigned for testing");
            }

            Debug.Log("✅ Bridge detection test complete");
        }

        private void TestLegacySystemIntegration()
        {
            Debug.Log("--- Test 2: Legacy System Integration ---");

            if (legacyProjectilePrefab == null)
            {
                Debug.LogWarning("No legacy projectile prefab - skipping test");
                return;
            }

            var legacyObj = Instantiate(legacyProjectilePrefab);
            var projectile = ProjectileSystemBridge.GetProjectileInterface(legacyObj);

            if (projectile != null)
            {
                // Test unified setup
                ProjectileSystemBridge.SetupProjectile(legacyObj, testDamage, testSpeed, testLifetime, testHoming, testScale, null);

                // Test property access
                Debug.Log($"Legacy Properties - Damage: {projectile.DamageAmount}, Speed: {projectile.BulletSpeed}, " +
                         $"Lifetime: {projectile.Lifetime}, Homing: {projectile.Homing}");

                // Test unified methods
                ProjectileSystemBridge.EnableHoming(legacyObj, false);
                Debug.Log($"Homing after disable: {projectile.Homing}");

                Debug.Log("✅ Legacy system integration working");
            }
            else
            {
                Debug.LogError("❌ Failed to get interface for legacy system");
            }

            Destroy(legacyObj);
        }

        private void TestNewSystemIntegration()
        {
            Debug.Log("--- Test 3: New System Integration ---");

            if (newProjectilePrefab == null)
            {
                Debug.LogWarning("No new projectile prefab - skipping test");
                return;
            }

            var newObj = Instantiate(newProjectilePrefab);
            var projectile = ProjectileSystemBridge.GetProjectileInterface(newObj);

            if (projectile != null)
            {
                // Test unified setup
                ProjectileSystemBridge.SetupProjectile(newObj, testDamage, testSpeed, testLifetime, testHoming, testScale, null);

                // Test property access
                Debug.Log($"New Properties - Damage: {projectile.DamageAmount}, Speed: {projectile.BulletSpeed}, " +
                         $"Lifetime: {projectile.Lifetime}, Homing: {projectile.Homing}");

                // Test unified methods
                ProjectileSystemBridge.EnableHoming(newObj, false);
                Debug.Log($"Homing after disable: {projectile.Homing}");

                Debug.Log("✅ New system integration working");
            }
            else
            {
                Debug.LogError("❌ Failed to get interface for new system");
            }

            Destroy(newObj);
        }

        private void TestUnifiedInterface()
        {
            Debug.Log("--- Test 4: Unified Interface ---");

            GameObject[] testObjects = new GameObject[2];
            
            // Create both types if available
            if (legacyProjectilePrefab != null)
                testObjects[0] = Instantiate(legacyProjectilePrefab);
            if (newProjectilePrefab != null)
                testObjects[1] = Instantiate(newProjectilePrefab);

            foreach (var obj in testObjects)
            {
                if (obj == null) continue;

                var properties = ProjectileSystemBridge.GetProjectileProperties(obj);
                if (properties != null)
                {
                    Debug.Log($"Unified Properties ({properties.SystemType}): " +
                             $"Damage: {properties.DamageAmount}, Speed: {properties.BulletSpeed}");

                    // Test property modification
                    properties.DamageAmount = 100f;
                    properties.BulletSpeed = 75f;
                    ProjectileSystemBridge.SetProjectileProperties(obj, properties);

                    var updatedProperties = ProjectileSystemBridge.GetProjectileProperties(obj);
                    Debug.Log($"Updated Properties ({updatedProperties.SystemType}): " +
                             $"Damage: {updatedProperties.DamageAmount}, Speed: {updatedProperties.BulletSpeed}");
                }

                Destroy(obj);
            }

            Debug.Log("✅ Unified interface test complete");
        }

        private void TestPoolBridge()
        {
            Debug.Log("--- Test 5: Pool Bridge ---");

            var poolBridge = FindObjectOfType<ProjectilePoolBridge>();
            if (poolBridge != null)
            {
                Debug.Log($"Pool Bridge found - Active: {poolBridge.GetActiveProjectileCount()}, " +
                         $"Available: {poolBridge.GetAvailableProjectileCount()}");

                // Test getting projectile from bridge pool
                var projectile = poolBridge.GetProjectile();
                if (projectile != null)
                {
                    Debug.Log($"Got projectile from bridge pool: {projectile.GetProjectileSystemType()}");
                    poolBridge.ReturnProjectile(projectile);
                    Debug.Log("Returned projectile to bridge pool");
                }

                Debug.Log("✅ Pool bridge test complete");
            }
            else
            {
                Debug.LogWarning("No ProjectilePoolBridge found - create one to test pool functionality");
            }
        }
    }
}
