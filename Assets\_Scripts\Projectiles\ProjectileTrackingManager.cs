using UnityEngine;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System;
using BTR.Projectiles;

namespace BTR
{
    /// <summary>
    /// Central manager for tracking all projectiles and their radar visibility.
    /// Acts as the single source of truth for projectile state.
    /// </summary>
    [DefaultExecutionOrder(-200)]  // After ProjectileManager (-250) but before RadarManager (-150)
    public class ProjectileTrackingManager : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool enableVisualDebugging = false;
        [SerializeField] private Color registeredColor = Color.green;
        [SerializeField] private Color unregisteredColor = Color.red;
        [SerializeField] private float debugLineLength = 2f;

        public static ProjectileTrackingManager Instance { get; private set; }

        // Concurrent dictionary to track all projectile states
        private readonly ConcurrentDictionary<int, ProjectileTrackingState> projectileStates = new ConcurrentDictionary<int, ProjectileTrackingState>();

        // Cache references
        private RadarManager radarManager;
        private ProjectileManager projectileManager;
        private ProjectileJobSystem jobSystem;

        // Debug info
        private readonly Dictionary<int, string> debugInfo = new Dictionary<int, string>();
        private float lastDebugUpdateTime;
        private const float DEBUG_UPDATE_INTERVAL = 0.5f;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }

        private void Start()
        {
            radarManager = RadarManager.Instance;
            projectileManager = ProjectileManager.Instance;
            if (projectileManager != null)
            {
                jobSystem = projectileManager.GetProjectileJobSystem();
            }

            if (!radarManager || !projectileManager || !jobSystem)
            {
                Debug.LogError("[ProjectileTrackingManager] Required dependencies not found!");
                enabled = false;
            }
        }

        private void LateUpdate()
        {
            if (!enabled) return;

            // Complete any pending jobs first
            jobSystem?.CompleteProjectileUpdate();

            // Update all tracked projectiles
            foreach (var kvp in projectileStates)
            {
                UpdateProjectileState(kvp.Key, kvp.Value);
            }

            // Update debug info periodically
            if (enableDebugLogging && Time.time - lastDebugUpdateTime > DEBUG_UPDATE_INTERVAL)
            {
                UpdateDebugInfo();
                lastDebugUpdateTime = Time.time;
            }
        }

        private void OnGUI()
        {
            if (!enableDebugLogging) return;

            // Draw a semi-transparent background
            GUI.color = new Color(0, 0, 0, 0.8f);
            GUI.Box(new Rect(5, 5, 310, Screen.height - 10), "");
            GUI.color = Color.white;

            GUILayout.BeginArea(new Rect(10, 10, 300, Screen.height - 20));

            // Header
            var headerStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 14,
                fontStyle = FontStyle.Bold,
                normal = { textColor = Color.yellow }
            };
            GUILayout.Label("=== PROJECTILE RADAR DEBUG ===", headerStyle);
            GUILayout.Label($"Total Tracked: {GetTrackedProjectileCount()}", headerStyle);
            GUILayout.Label($"Total Registered: {GetRegisteredProjectileCount()}", headerStyle);
            GUILayout.Space(10);

            // Projectile list
            var labelStyle = new GUIStyle(GUI.skin.label) { richText = true };
            foreach (var info in debugInfo.Values)
            {
                GUILayout.Label(info, labelStyle);
            }

            GUILayout.EndArea();
        }

        private void UpdateDebugInfo()
        {
            debugInfo.Clear();
            foreach (var kvp in projectileStates)
            {
                var state = kvp.Value;
                if (state.Projectile == null) continue;

                string status = state.IsRegistered ? "REGISTERED" : "UNREGISTERED";
                string color = state.IsRegistered ? "green" : "red";

                debugInfo[kvp.Key] = string.Format(
                    "<color={0}>[{1}]</color> ID: {2}\n" +
                    "Position: {3}\n" +
                    "Homing: {4}, HasTarget: {5}\n" +
                    "JobIdx: {6}, Active: {7}\n" +
                    "-------------------",
                    color,
                    status,
                    kvp.Key,
                    state.Transform?.position.ToString("F1") ?? "NULL",
                    state.IsHoming,
                    state.HasTarget,
                    state.JobSystemIndex,
                    state.IsActive
                );
            }
        }

        private void OnDrawGizmos()
        {
            if (!enableVisualDebugging || !Application.isPlaying) return;

            foreach (var state in projectileStates.Values)
            {
                if (state.Transform == null) continue;

                Vector3 start = state.Transform.position;
                Vector3 up = Vector3.up * debugLineLength;

                // Draw vertical line
                Gizmos.color = state.IsRegistered ? registeredColor : unregisteredColor;
                Gizmos.DrawLine(start, start + up);

                // Draw sphere at projectile position
                Gizmos.DrawWireSphere(start, 0.5f);

                // Draw arrow pointing to radar blip
                Vector3 arrowTop = start + up;
                float arrowSize = 0.3f;
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.left * arrowSize);
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.right * arrowSize);

                // Add text label with background
#if UNITY_EDITOR
                UnityEditor.Handles.BeginGUI();
                Vector3 screenPoint = UnityEditor.Handles.matrix.MultiplyPoint(start + up);
                if (screenPoint.z > 0) // Only draw if in front of camera
                {
                    Vector2 guiPoint = UnityEditor.HandleUtility.WorldToGUIPoint(start + up);
                    string label = $"ID: {state.Projectile.GetInstanceID()}\n" +
                                  $"Registered: {state.IsRegistered}\n" +
                                  $"Homing: {state.IsHoming}\n" +
                                  $"HasTarget: {state.HasTarget}";

                    var style = new GUIStyle(GUI.skin.box);
                    style.normal.textColor = state.IsRegistered ? registeredColor : unregisteredColor;
                    style.alignment = TextAnchor.MiddleCenter;
                    style.fontSize = 12;
                    style.fontStyle = FontStyle.Bold;

                    Vector2 size = style.CalcSize(new GUIContent(label));
                    GUI.Box(new Rect(guiPoint.x - size.x / 2, guiPoint.y - size.y / 2, size.x, size.y), label, style);
                }
                UnityEditor.Handles.EndGUI();
#endif
            }
        }

        public void RegisterProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            int id = projectile.GetInstanceID();
            var state = new ProjectileTrackingState
            {
                Projectile = projectile,
                Transform = projectile.transform,
                IsHoming = projectile.homing,
                HasTarget = projectile.currentTarget != null,
                IsActive = projectile.gameObject.activeInHierarchy,
                JobSystemIndex = projectile.projectileIndex,
                RadarIconIndex = projectile.GetComponent<ProjectileRadarTracker>()?.radarIconIndex ?? 0
            };

            projectileStates.TryAdd(id, state);

            if (enableDebugLogging)
            {
                Debug.Log($"[{GetType().Name}] Registered projectile {id} - Homing: {state.IsHoming}, HasTarget: {state.HasTarget}");
            }

            // If it should be on radar, register immediately
            if (ShouldBeOnRadar(state))
            {
                RegisterWithRadar(state);
            }
        }

        /// <summary>
        /// Register a projectile using the unified IProjectile interface
        /// </summary>
        public void RegisterProjectileInterface(IProjectile projectile)
        {
            if (projectile == null) return;

            if (projectile.GetProjectileSystemType() == ProjectileSystemType.Legacy)
            {
                // For legacy projectiles, use the existing method
                var adapter = projectile as ProjectileStateBased_IProjectileAdapter;
                var legacyProjectile = adapter?.GetProjectileStateBased();
                if (legacyProjectile != null)
                {
                    RegisterProjectile(legacyProjectile);
                }
            }
            else
            {
                // For new system projectiles, create a new tracking state
                RegisterNewSystemProjectile(projectile);
            }
        }

        /// <summary>
        /// Register a new system projectile (ProjectileEntity)
        /// </summary>
        private void RegisterNewSystemProjectile(IProjectile projectile)
        {
            int id = projectile.InstanceID;
            var entity = projectile as ProjectileEntity;
            if (entity == null) return;

            var state = new ProjectileTrackingState
            {
                ProjectileInterface = projectile,
                Transform = projectile.Transform,
                IsHoming = entity.Homing,
                HasTarget = entity.CurrentTarget != null,
                IsActive = projectile.GameObject.activeInHierarchy,
                JobSystemIndex = projectile.ProjectileIndex,
                RadarIconIndex = entity.GetComponent<ProjectileRadarTracker>()?.radarIconIndex ?? 0
            };

            projectileStates.TryAdd(id, state);

            if (enableDebugLogging)
            {
                Debug.Log($"[{GetType().Name}] Registered new system projectile {id} - Homing: {state.IsHoming}, HasTarget: {state.HasTarget}");
            }

            // If it should be on radar, register immediately
            if (ShouldBeOnRadar(state))
            {
                RegisterWithRadar(state);
            }
        }

        public void UnregisterProjectile(ProjectileStateBased projectile)
        {
            if (projectile == null) return;

            int id = projectile.GetInstanceID();
            if (projectileStates.TryRemove(id, out var state))
            {
                if (enableDebugLogging)
                {
                    Debug.Log($"[{GetType().Name}] Unregistered projectile {id} - Was registered: {state.IsRegistered}");
                }
                UnregisterFromRadar(state);
            }
        }

        private void UpdateProjectileState(int id, ProjectileTrackingState state)
        {
            if (!state.IsValid())
            {
                projectileStates.TryRemove(id, out _);
                return;
            }

            bool wasHoming = state.IsHoming;
            bool wasRegistered = state.IsRegistered;

            // Update current state based on projectile type
            if (state.GetSystemType() == ProjectileSystemType.Legacy && state.Projectile != null)
            {
                // Legacy projectile update
                state.IsHoming = state.Projectile.homing;
                state.HasTarget = state.Projectile.currentTarget != null;
                state.IsActive = state.Projectile.gameObject.activeInHierarchy;
                state.JobSystemIndex = state.Projectile.projectileIndex;
            }
            else if (state.ProjectileInterface != null)
            {
                // New system projectile update
                var entity = state.ProjectileInterface as ProjectileEntity;
                if (entity != null)
                {
                    state.IsHoming = entity.Homing;
                    state.HasTarget = entity.CurrentTarget != null;
                    state.IsActive = state.ProjectileInterface.GameObject.activeInHierarchy;
                    state.JobSystemIndex = state.ProjectileInterface.ProjectileIndex;
                }
            }

            bool shouldBeOnRadar = ShouldBeOnRadar(state);

            // Handle radar registration changes
            if (shouldBeOnRadar && !state.IsRegistered)
            {
                RegisterWithRadar(state);
                if (enableDebugLogging)
                {
                    Debug.Log($"[{GetType().Name}] Registering projectile {id} with radar - State change detected");
                }
            }
            else if (!shouldBeOnRadar && state.IsRegistered)
            {
                UnregisterFromRadar(state);
                if (enableDebugLogging)
                {
                    Debug.Log($"[{GetType().Name}] Unregistering projectile {id} from radar - State change detected");
                }
            }

            // Log significant state changes
            if (enableDebugLogging && (wasHoming != state.IsHoming || wasRegistered != state.IsRegistered))
            {
                Debug.Log($"[{GetType().Name}] State change for projectile {id} ({state.GetSystemType()}):\nHoming: {wasHoming}->{state.IsHoming}\nRegistered: {wasRegistered}->{state.IsRegistered}\nHasTarget: {state.HasTarget}, Active: {state.IsActive}");
            }
        }

        private bool ShouldBeOnRadar(ProjectileTrackingState state)
        {
            if (!state.IsActive || !state.IsHoming || !state.HasTarget) return false;

            // For legacy projectiles, validate job system state
            if (state.GetSystemType() == ProjectileSystemType.Legacy && state.JobSystemIndex >= 0 && jobSystem != null)
            {
                return jobSystem.IsProjectileActive(state.JobSystemIndex);
            }

            // For new system projectiles, they should appear on radar if they meet basic criteria
            // (active, homing, has target) since they're not yet integrated with job system
            if (state.GetSystemType() != ProjectileSystemType.Legacy)
            {
                return true; // New projectiles bypass job system check for now
            }

            return true;
        }

        private void RegisterWithRadar(ProjectileTrackingState state)
        {
            if (!radarManager || !state.Transform)
            {
                if (enableDebugLogging)
                {
                    Debug.LogWarning($"[ProjectileTrackingManager] Cannot register with radar - RadarManager: {radarManager != null}, Transform: {state.Transform != null}");
                }
                return;
            }

            try
            {
                radarManager.CompleteProjectileUpdate();
                radarManager.RegisterEntity(
                    state.Transform,
                    state.Transform.position,
                    state.RadarIconIndex,
                    true  // Dynamic entity
                );
                state.IsRegistered = true;

                if (enableDebugLogging)
                {
                    Debug.Log($"[ProjectileTrackingManager] Successfully registered {state.GetSystemType()} projectile with radar - IconIndex: {state.RadarIconIndex}, Position: {state.Transform.position}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileTrackingManager] Error registering projectile with radar: {e.Message}");
            }
        }

        private void UnregisterFromRadar(ProjectileTrackingState state)
        {
            if (!radarManager || !state.Transform || !state.IsRegistered) return;

            radarManager.CompleteProjectileUpdate();
            radarManager.UnregisterEntity(state.Transform);
            state.IsRegistered = false;
        }

        private void OnDestroy()
        {
            // Clean up all registrations
            foreach (var state in projectileStates.Values)
            {
                UnregisterFromRadar(state);
            }
            projectileStates.Clear();
        }

        // Debug helper methods
        public void GetProjectileDebugInfo(int projectileId, out bool isRegistered, out bool isHoming, out bool hasTarget)
        {
            if (projectileStates.TryGetValue(projectileId, out var state))
            {
                isRegistered = state.IsRegistered;
                isHoming = state.IsHoming;
                hasTarget = state.HasTarget;
            }
            else
            {
                isRegistered = false;
                isHoming = false;
                hasTarget = false;
            }
        }

        public int GetTrackedProjectileCount()
        {
            return projectileStates.Count;
        }

        public int GetRegisteredProjectileCount()
        {
            int count = 0;
            foreach (var state in projectileStates.Values)
            {
                if (state.IsRegistered) count++;
            }
            return count;
        }
    }

    /// <summary>
    /// Represents the complete tracking state of a projectile
    /// </summary>
    public class ProjectileTrackingState
    {
        // Legacy system support
        public ProjectileStateBased Projectile;

        // New system support
        public IProjectile ProjectileInterface;

        // Common properties
        public Transform Transform;
        public bool IsHoming;
        public bool HasTarget;
        public bool IsActive;
        public int JobSystemIndex;
        public int RadarIconIndex;
        public bool IsRegistered;

        /// <summary>
        /// Get the projectile system type
        /// </summary>
        public ProjectileSystemType GetSystemType()
        {
            if (ProjectileInterface != null)
                return ProjectileInterface.GetProjectileSystemType();
            else if (Projectile != null)
                return ProjectileSystemType.Legacy;
            else
                return ProjectileSystemType.Legacy; // Default fallback
        }

        /// <summary>
        /// Check if this tracking state has a valid projectile
        /// </summary>
        public bool IsValid()
        {
            return (Projectile != null && Projectile.gameObject != null) ||
                   (ProjectileInterface != null && ProjectileInterface.GameObject != null);
        }
    }
}